const fs = require('fs').promises;
const path = require('path');

class ReactionRoleStorage {
    constructor() {
        this.filePath = path.join(__dirname, '..', 'reaction_roles.json');
        this.isWriting = false;
        this.writeQueue = [];
        // In-memory storage for setup sessions (temporary data)
        this.setupSessions = new Map();
    }

    /**
     * Read the reaction roles data from JSON file
     * @returns {Promise<Object>} The reaction roles data
     */
    async readData() {
        try {
            const data = await fs.readFile(this.filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                // File doesn't exist, create it with default structure
                const defaultData = { panels: {} };
                await this.writeData(defaultData);
                return defaultData;
            }
            console.error('Error reading reaction roles data:', error);
            throw error;
        }
    }

    /**
     * Write data to JSON file with queue system for thread safety
     * @param {Object} data - The data to write
     * @returns {Promise<void>}
     */
    async writeData(data) {
        return new Promise((resolve, reject) => {
            this.writeQueue.push({ data, resolve, reject });
            this.processWriteQueue();
        });
    }

    /**
     * Process the write queue to ensure thread-safe operations
     */
    async processWriteQueue() {
        if (this.isWriting || this.writeQueue.length === 0) {
            return;
        }

        this.isWriting = true;
        const { data, resolve, reject } = this.writeQueue.shift();

        try {
            await fs.writeFile(this.filePath, JSON.stringify(data, null, 2), 'utf8');
            resolve();
        } catch (error) {
            console.error('Error writing reaction roles data:', error);
            reject(error);
        } finally {
            this.isWriting = false;
            // Process next item in queue if any
            if (this.writeQueue.length > 0) {
                setImmediate(() => this.processWriteQueue());
            }
        }
    }

    /**
     * Add a new reaction role panel
     * @param {string} panelId - Unique panel identifier
     * @param {Object} panelData - Panel configuration data
     * @returns {Promise<void>}
     */
    async addPanel(panelId, panelData) {
        const data = await this.readData();
        data.panels[panelId] = panelData;
        await this.writeData(data);
    }

    /**
     * Remove a reaction role panel
     * @param {string} panelId - Panel identifier to remove
     * @returns {Promise<boolean>} True if panel was removed, false if not found
     */
    async removePanel(panelId) {
        const data = await this.readData();
        if (data.panels[panelId]) {
            delete data.panels[panelId];
            await this.writeData(data);
            return true;
        }
        return false;
    }

    /**
     * Get a specific panel by ID
     * @param {string} panelId - Panel identifier
     * @returns {Promise<Object|null>} Panel data or null if not found
     */
    async getPanel(panelId) {
        const data = await this.readData();
        return data.panels[panelId] || null;
    }

    /**
     * Get all panels
     * @returns {Promise<Object>} All panels data
     */
    async getAllPanels() {
        const data = await this.readData();
        return data.panels;
    }

    /**
     * Get panels by guild ID
     * @param {string} guildId - Guild identifier
     * @returns {Promise<Object>} Panels for the specified guild
     */
    async getPanelsByGuild(guildId) {
        const data = await this.readData();
        const guildPanels = {};
        
        for (const [panelId, panelData] of Object.entries(data.panels)) {
            if (panelData.guild_id === guildId) {
                guildPanels[panelId] = panelData;
            }
        }
        
        return guildPanels;
    }

    /**
     * Update a panel's data
     * @param {string} panelId - Panel identifier
     * @param {Object} updates - Partial panel data to update
     * @returns {Promise<boolean>} True if panel was updated, false if not found
     */
    async updatePanel(panelId, updates) {
        const data = await this.readData();
        if (data.panels[panelId]) {
            data.panels[panelId] = { ...data.panels[panelId], ...updates };
            await this.writeData(data);
            return true;
        }
        return false;
    }

    /**
     * Generate a unique panel ID
     * @returns {string} Unique panel identifier
     */
    generatePanelId() {
        return `panel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Setup Session Management Methods

    /**
     * Create a new setup session
     * @param {string} userId - User ID who started the setup
     * @param {string} guildId - Guild ID where setup is happening
     * @param {string} channelId - Target channel ID for the panel
     * @returns {string} Session ID
     */
    createSetupSession(userId, guildId, channelId) {
        const sessionId = `setup_${userId}_${Date.now()}`;
        const sessionData = {
            userId,
            guildId,
            channelId,
            title: null,
            description: null,
            mode: null,
            imageUrl: null,
            roles: [],
            createdAt: Date.now()
        };

        this.setupSessions.set(sessionId, sessionData);

        // Clean up old sessions (older than 30 minutes)
        this.cleanupOldSessions();

        return sessionId;
    }

    /**
     * Get setup session data
     * @param {string} sessionId - Session ID
     * @returns {Object|null} Session data or null if not found
     */
    getSetupSession(sessionId) {
        return this.setupSessions.get(sessionId) || null;
    }

    /**
     * Update setup session data
     * @param {string} sessionId - Session ID
     * @param {Object} updates - Partial session data to update
     * @returns {boolean} True if session was updated, false if not found
     */
    updateSetupSession(sessionId, updates) {
        const session = this.setupSessions.get(sessionId);
        if (session) {
            Object.assign(session, updates);
            return true;
        }
        return false;
    }

    /**
     * Delete setup session
     * @param {string} sessionId - Session ID
     * @returns {boolean} True if session was deleted, false if not found
     */
    deleteSetupSession(sessionId) {
        return this.setupSessions.delete(sessionId);
    }

    /**
     * Clean up old setup sessions (older than 30 minutes)
     */
    cleanupOldSessions() {
        const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
        for (const [sessionId, sessionData] of this.setupSessions.entries()) {
            if (sessionData.createdAt < thirtyMinutesAgo) {
                this.setupSessions.delete(sessionId);
            }
        }
    }

    /**
     * Get setup sessions by user ID
     * @param {string} userId - User ID
     * @returns {Array} Array of session IDs for the user
     */
    getUserSetupSessions(userId) {
        const userSessions = [];
        for (const [sessionId, sessionData] of this.setupSessions.entries()) {
            if (sessionData.userId === userId) {
                userSessions.push(sessionId);
            }
        }
        return userSessions;
    }
}

module.exports = new ReactionRoleStorage();
