const { PermissionFlagsBits } = require('discord.js');
const reactionRoleStorage = require('./reactionRoleStorage');

class ReactionRoleHandler {
    /**
     * Handle reaction role button interactions
     * @param {ButtonInteraction} interaction - The button interaction
     */
    async handleReactionRoleButton(interaction) {
        try {
            // Extract role ID from custom ID
            const roleId = interaction.customId.replace('reaction_role_', '');
            
            // Find the panel this button belongs to
            const panelData = await this.findPanelByMessage(interaction.guild.id, interaction.message.id);
            
            if (!panelData) {
                return interaction.reply({
                    content: '❌ This reaction role panel is no longer configured. Please contact an administrator.',
                    ephemeral: true
                });
            }

            // Check if the role exists in the panel configuration
            const roleConfig = panelData.roles.find(r => r.role_id === roleId);
            if (!roleConfig) {
                return interaction.reply({
                    content: '❌ This role is no longer configured for this panel.',
                    ephemeral: true
                });
            }

            // Get the role object
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                return interaction.reply({
                    content: '❌ This role no longer exists on the server.',
                    ephemeral: true
                });
            }

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            if (!botMember.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return interaction.reply({
                    content: '❌ I don\'t have permission to manage roles.',
                    ephemeral: true
                });
            }

            // Check role hierarchy
            if (role.position >= botMember.roles.highest.position) {
                return interaction.reply({
                    content: `❌ I cannot manage the role "${role.name}" because it's higher than or equal to my highest role.`,
                    ephemeral: true
                });
            }

            if (role.managed) {
                return interaction.reply({
                    content: `❌ The role "${role.name}" is managed by an integration and cannot be assigned.`,
                    ephemeral: true
                });
            }

            // Get member
            const member = interaction.member;
            const hasRole = member.roles.cache.has(roleId);

            // Handle single role mode
            if (panelData.mode === 'single' && !hasRole) {
                await this.handleSingleRoleMode(interaction, member, role, panelData);
            } else {
                // Handle multi role mode or role removal
                await this.handleMultiRoleMode(interaction, member, role, hasRole);
            }

        } catch (error) {
            console.error('Error handling reaction role button:', error);
            
            if (!interaction.replied) {
                await interaction.reply({
                    content: `❌ An error occurred while processing your role request: ${error.message}`,
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Handle single role mode (remove other roles from same panel)
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {GuildMember} member - The guild member
     * @param {Role} role - The role to assign
     * @param {Object} panelData - The panel configuration
     */
    async handleSingleRoleMode(interaction, member, role, panelData) {
        try {
            // Remove all other roles from this panel
            const rolesToRemove = [];
            for (const roleConfig of panelData.roles) {
                if (roleConfig.role_id !== role.id && member.roles.cache.has(roleConfig.role_id)) {
                    rolesToRemove.push(roleConfig.role_id);
                }
            }

            // Remove old roles and add new role
            if (rolesToRemove.length > 0) {
                await member.roles.remove(rolesToRemove);
            }
            await member.roles.add(role);

            const removedRoleNames = rolesToRemove.map(id => {
                const r = interaction.guild.roles.cache.get(id);
                return r ? r.name : 'Unknown Role';
            });

            let message = `✅ You now have the **${role.name}** role!`;
            if (removedRoleNames.length > 0) {
                message += `\n🔄 Removed: ${removedRoleNames.join(', ')}`;
            }

            await interaction.reply({
                content: message,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error in single role mode:', error);
            await interaction.reply({
                content: `❌ Failed to update your roles: ${error.message}`,
                ephemeral: true
            });
        }
    }

    /**
     * Handle multi role mode (toggle role)
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {GuildMember} member - The guild member
     * @param {Role} role - The role to toggle
     * @param {boolean} hasRole - Whether the member already has the role
     */
    async handleMultiRoleMode(interaction, member, role, hasRole) {
        try {
            if (hasRole) {
                // Remove role
                await member.roles.remove(role);
                await interaction.reply({
                    content: `❌ Removed the **${role.name}** role!`,
                    ephemeral: true
                });
            } else {
                // Add role
                await member.roles.add(role);
                await interaction.reply({
                    content: `✅ Added the **${role.name}** role!`,
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Error in multi role mode:', error);
            await interaction.reply({
                content: `❌ Failed to update your role: ${error.message}`,
                ephemeral: true
            });
        }
    }

    /**
     * Find panel data by message ID
     * @param {string} guildId - Guild ID
     * @param {string} messageId - Message ID
     * @returns {Promise<Object|null>} Panel data or null if not found
     */
    async findPanelByMessage(guildId, messageId) {
        try {
            const guildPanels = await reactionRoleStorage.getPanelsByGuild(guildId);
            
            for (const [panelId, panelData] of Object.entries(guildPanels)) {
                if (panelData.message_id === messageId) {
                    return panelData;
                }
            }
            
            return null;
        } catch (error) {
            console.error('Error finding panel by message:', error);
            return null;
        }
    }

    /**
     * Check if a custom ID is a reaction role interaction
     * @param {string} customId - The custom ID to check
     * @returns {boolean} True if it's a reaction role interaction
     */
    isReactionRoleInteraction(customId) {
        return customId.startsWith('reaction_role_');
    }

    /**
     * Handle reaction role interactions (button clicks)
     * @param {ButtonInteraction} interaction - The button interaction
     */
    async handleReactionRoleInteraction(interaction) {
        if (this.isReactionRoleInteraction(interaction.customId)) {
            await this.handleReactionRoleButton(interaction);
        }
    }

    /**
     * Create a reaction role panel
     * @param {TextChannel} channel - The channel to send the panel to
     * @param {Object} panelData - Panel configuration data
     * @param {Guild} guild - The Discord guild
     * @returns {Promise<Object>} Result object with success status
     */
    async createReactionRolePanel(channel, panelData, guild) {
        try {
            const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            // Create the embed
            const embed = new EmbedBuilder()
                .setTitle(panelData.title)
                .setDescription(panelData.description)
                .setColor(panelData.color ? parseInt(panelData.color.replace('#', ''), 16) : 0x5865F2)
                .setFooter({
                    text: `Mode: ${panelData.mode === 'single' ? 'Single Role (selecting one removes others)' : 'Multiple Roles (toggle on/off)'} • ${panelData.roles.length} role${panelData.roles.length !== 1 ? 's' : ''} available`,
                    iconURL: guild.iconURL()
                })
                .setTimestamp();

            if (panelData.imageUrl) {
                embed.setImage(panelData.imageUrl);
            }

            // Create buttons (up to 25 roles, 5 per row)
            const components = [];
            const roles = panelData.roles;

            for (let i = 0; i < roles.length; i += 5) {
                const row = new ActionRowBuilder();
                const rowRoles = roles.slice(i, i + 5);

                for (const role of rowRoles) {
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId(`reaction_role_${role.role_id}`)
                            .setLabel(`${role.emoji} ${role.label}`)
                            .setStyle(role.buttonStyle || ButtonStyle.Secondary)
                    );
                }

                components.push(row);
            }

            // Send the panel message
            const message = await channel.send({
                embeds: [embed],
                components: components
            });

            // Store the panel data
            const panelId = `panel_${guild.id}_${Date.now()}`;
            const fullPanelData = {
                ...panelData,
                panel_id: panelId,
                guild_id: guild.id,
                channel_id: channel.id,
                message_id: message.id,
                created_at: new Date().toISOString()
            };

            await reactionRoleStorage.addPanel(panelId, fullPanelData);

            return {
                success: true,
                panelId: panelId,
                messageId: message.id
            };

        } catch (error) {
            console.error('Error creating reaction role panel:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = new ReactionRoleHandler();
