const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

// In-memory storage for conversation states (only for Add Role functionality)
const conversationStates = new Map();

// Cleanup old configurations every 30 minutes
setInterval(() => {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    for (const [configId, config] of dashboardConfigs.entries()) {
        if (now - config.createdAt > maxAge) {
            dashboardConfigs.delete(configId);
            console.log(`[CLEANUP] Removed expired config: ${configId}`);
        }
    }

    for (const [userId, state] of conversationStates.entries()) {
        if (now - state.createdAt > maxAge) {
            conversationStates.delete(userId);
            console.log(`[CLEANUP] Removed expired conversation state for user: ${userId}`);
        }
    }
}, 30 * 60 * 1000);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        const channel = interaction.options.getChannel('channel');

        // Validate bot permissions
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create unique configuration ID
        const configId = `${interaction.user.id}_${Date.now()}`;

        // Initialize configuration
        dashboardConfigs.set(configId, {
            userId: interaction.user.id,
            channelId: channel.id,
            title: null,
            description: null,
            mode: null,
            roles: [],
            createdAt: Date.now()
        });

        console.log(`[DEBUG] Created config with ID: ${configId} for user: ${interaction.user.id}`);

        // Show dashboard
        await this.createDashboard(interaction, configId, false);
    },

    // Helper function to check if configuration is complete
    isConfigComplete(config) {
        return config.title && config.description && config.mode && config.roles.length > 0;
    },

    // Create or update dashboard display
    async createDashboard(interaction, configId, isUpdate = false) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        const isComplete = this.isConfigComplete(config);

        const embed = new EmbedBuilder()
            .setTitle('🎛️ Reaction Role Panel Setup')
            .setDescription('Configure your reaction role panel using the buttons below.')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '📝 Title',
                    value: config.title || '❌ Not set',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: config.description ? (config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description) : '❌ Not set',
                    inline: true
                },
                {
                    name: '⚙️ Mode',
                    value: config.mode || '❌ Not set',
                    inline: true
                },
                {
                    name: '🎭 Roles',
                    value: config.roles.length > 0 ?
                        config.roles.map((role, index) => `${index + 1}. ${role.emoji} **${role.label}** (<@&${role.role_id}>)`).join('\n') :
                        '❌ No roles added',
                    inline: false
                }
            )
            .setFooter({
                text: isComplete ? '✅ Configuration complete! You can create the panel.' : '⚠️ Complete all fields to create the panel.'
            });

        // Create action buttons
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_title_${configId}`)
                    .setLabel('Set Title')
                    .setStyle(config.title ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_description_${configId}`)
                    .setLabel('Set Description')
                    .setStyle(config.description ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_mode_${configId}`)
                    .setLabel('Set Mode')
                    .setStyle(config.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('⚙️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_addrole_${configId}`)
                    .setLabel('Add Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_removerole_${configId}`)
                    .setLabel('Remove Last Role')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('➖')
                    .setDisabled(config.roles.length === 0),
                new ButtonBuilder()
                    .setCustomId(`dashboard_preview_${configId}`)
                    .setLabel('Preview')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👁️')
                    .setDisabled(!isComplete)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_create_${configId}`)
                    .setLabel('Create Panel')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_cancel_${configId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const components = [row1, row2, row3];

        if (isUpdate) {
            try {
                await interaction.editReply({
                    embeds: [embed],
                    components: components
                });
            } catch (error) {
                console.error('Error updating dashboard:', error);
                await interaction.followUp({
                    embeds: [embed],
                    components: components,
                    flags: MessageFlags.Ephemeral
                });
            }
        } else {
            await interaction.reply({
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Handle dashboard button interactions
    async handleDashboardButton(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        console.log(`[DEBUG] Dashboard button: ${action}, configId: ${configId}`);

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                await this.showTitleModal(interaction, configId);
                break;
            case 'description':
                await this.showDescriptionModal(interaction, configId);
                break;
            case 'mode':
                await this.showModeSelect(interaction, configId);
                break;
            case 'addrole':
                await this.startRoleConversation(interaction, configId);
                break;
            case 'removerole':
                await this.removeLastRole(interaction, configId);
                break;
            case 'preview':
                await this.showPreview(interaction, configId);
                break;
            case 'create':
                await this.createPanel(interaction, configId);
                break;
            case 'cancel':
                await this.cancelSetup(interaction, configId);
                break;
            default:
                await interaction.reply({
                    content: '❌ Unknown action.',
                    flags: MessageFlags.Ephemeral
                });
        }
    },

    // Show title modal
    async showTitleModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_title_${configId}`)
            .setTitle('Set Panel Title');

        const titleInput = new TextInputBuilder()
            .setCustomId('title')
            .setLabel('Panel Title')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the title for your reaction role panel')
            .setRequired(true)
            .setMaxLength(256);

        const firstActionRow = new ActionRowBuilder().addComponents(titleInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show description modal
    async showDescriptionModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_description_${configId}`)
            .setTitle('Set Panel Description');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Panel Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the description for your reaction role panel')
            .setRequired(true)
            .setMaxLength(4000);

        const firstActionRow = new ActionRowBuilder().addComponents(descriptionInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show mode selection
    async showModeSelect(interaction, configId) {
        const select = new StringSelectMenuBuilder()
            .setCustomId(`select_mode_${configId}`)
            .setPlaceholder('Choose reaction role mode')
            .addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('Single Role')
                    .setDescription('Users can only have one role from this panel at a time')
                    .setValue('single')
                    .setEmoji('1️⃣'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Multiple Roles')
                    .setDescription('Users can have multiple roles from this panel')
                    .setValue('multiple')
                    .setEmoji('🔢')
            );

        const row = new ActionRowBuilder().addComponents(select);

        await interaction.reply({
            content: '**Select the reaction role mode:**',
            components: [row],
            flags: MessageFlags.Ephemeral
        });
    },

    // Start conversation-based role addition
    async startRoleConversation(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Check if user already has an active conversation
        if (conversationStates.has(interaction.user.id)) {
            return await interaction.reply({
                content: '❌ **You already have an active role conversation in progress.**\n\nPlease complete your current role addition before starting a new one.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Initialize conversation state
        conversationStates.set(interaction.user.id, {
            configId: configId,
            step: 'roles',
            tempRoles: [],
            currentRoleIndex: 0,
            createdAt: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎭 Add Roles - Step 1')
            .setDescription('**Please provide your roles and emojis in this format:**\n' +
                '`@role emoji @role emoji @role emoji`\n\n' +
                '**Examples:**\n' +
                '• `@Member 🎉 @VIP ⭐ @Admin 👑`\n' +
                '• `@Red Team 🔴 @Blue Team 🔵`\n' +
                '• `@Notifications 🔔`\n\n' +
                '**Instructions:**\n' +
                '• Mention each role followed by its emoji\n' +
                '• You can add multiple role-emoji pairs in one message\n' +
                '• Use any emoji (Unicode or custom server emojis)\n' +
                '• Maximum 25 roles total per panel\n\n' +
                '**Type your roles and emojis now:**')
            .setColor(0x3498db)
            .setFooter({ text: 'You have 5 minutes to respond. Type "cancel" to abort.' });

        await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleRoleInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle role input parsing for conversation
    async handleRoleInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Parse role mentions and emojis
        const roleEmojiPairs = this.parseRoleEmojiInput(message.content, interaction.guild);

        if (!roleEmojiPairs.success) {
            await interaction.followUp({
                content: `❌ **Error:** ${roleEmojiPairs.error}\n\nPlease try again with the correct format: \`@role emoji @role emoji\``,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection
            setTimeout(() => this.startRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Check total role limit
        const config = dashboardConfigs.get(state.configId);
        if (config.roles.length + roleEmojiPairs.roles.length > 25) {
            await interaction.followUp({
                content: `❌ **Too many roles:** You can only have a maximum of 25 roles per panel. You currently have ${config.roles.length} roles and are trying to add ${roleEmojiPairs.roles.length} more.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection
            setTimeout(() => this.startRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Validate roles
        const validation = reactionRoleValidator.validateRoleHierarchy(roleEmojiPairs.roles, interaction.guild.members.me);
        if (!validation.success) {
            await interaction.followUp({
                content: `❌ **Role Validation Error:** ${validation.error}\n\nPlease try again with valid roles.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection
            setTimeout(() => this.startRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Store roles and start label collection
        state.tempRoles = roleEmojiPairs.roles;
        state.step = 'labels';
        state.currentRoleIndex = 0;

        await this.startLabelCollection(interaction);
    },

    // Parse role mentions and emojis from user input
    parseRoleEmojiInput(content, guild) {
        // Extract role mentions and emojis
        const rolePattern = /<@&(\d+)>/g;
        const emojiPattern = /(?:<a?:\w+:\d+>|[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}])/gu;

        const roleMentions = [...content.matchAll(rolePattern)];
        const emojis = [...content.matchAll(emojiPattern)];

        if (roleMentions.length === 0) {
            return { success: false, error: 'No role mentions found. Please mention roles using @role format.' };
        }

        if (emojis.length === 0) {
            return { success: false, error: 'No emojis found. Please include emojis after each role mention.' };
        }

        if (roleMentions.length !== emojis.length) {
            return { success: false, error: `Mismatch: Found ${roleMentions.length} roles but ${emojis.length} emojis. Each role needs exactly one emoji.` };
        }

        const roles = [];
        for (let i = 0; i < roleMentions.length; i++) {
            const roleId = roleMentions[i][1];
            const emoji = emojis[i][0];
            const role = guild.roles.cache.get(roleId);

            if (!role) {
                return { success: false, error: `Role with ID ${roleId} not found.` };
            }

            roles.push({
                role_id: roleId,
                emoji: emoji,
                label: role.name // Default label, will be customized later
            });
        }

        return { success: true, roles };
    },

    // Start label collection for each role
    async startLabelCollection(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state || state.currentRoleIndex >= state.tempRoles.length) {
            // All labels collected, add roles to config and return to dashboard
            await this.finishRoleAddition(interaction);
            return;
        }

        const currentRole = state.tempRoles[state.currentRoleIndex];
        const role = interaction.guild.roles.cache.get(currentRole.role_id);

        const embed = new EmbedBuilder()
            .setTitle(`🎭 Add Roles - Step 2 (${state.currentRoleIndex + 1}/${state.tempRoles.length})`)
            .setDescription(`**Configuring label for role:** ${role}\n\n` +
                `**Current emoji:** ${currentRole.emoji}\n` +
                `**Default label:** ${currentRole.label}\n\n` +
                `**What label would you like for the ${role.name} role?**\n\n` +
                `*You can press Enter to keep the default label "${currentRole.label}" or type a custom label.*`)
            .setColor(0xe74c3c)
            .setFooter({ text: 'Type your custom label or press Enter for default. Type "cancel" to abort.' });

        await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector for label
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleLabelInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle label input
    async handleLabelInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Update label (use custom label if provided, otherwise keep default)
        const customLabel = message.content.trim();
        if (customLabel && customLabel.length > 0) {
            // Validate label
            const labelValidation = reactionRoleValidator.validateTitle(customLabel);
            if (!labelValidation.success) {
                await interaction.followUp({
                    content: `❌ **Invalid label:** ${labelValidation.error}\n\nPlease try again with a valid label.`,
                    flags: MessageFlags.Ephemeral
                });

                // Restart label collection for current role
                setTimeout(() => this.startLabelCollection(interaction), 2000);
                return;
            }

            state.tempRoles[state.currentRoleIndex].label = customLabel;
        }

        // Move to next role
        state.currentRoleIndex++;
        await this.startLabelCollection(interaction);
    },

    // Finish role addition and return to dashboard
    async finishRoleAddition(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        const config = dashboardConfigs.get(state.configId);
        if (!config) {
            conversationStates.delete(interaction.user.id);
            return await interaction.followUp({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Add new roles to config
        config.roles.push(...state.tempRoles);

        // Clean up conversation state
        conversationStates.delete(interaction.user.id);

        // Show success message
        await interaction.followUp({
            content: `✅ **Successfully added ${state.tempRoles.length} role(s) to your panel!**\n\nReturning to dashboard...`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard
        setTimeout(() => this.createDashboard(interaction, state.configId, true), 2000);
    },

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                const title = interaction.fields.getTextInputValue('title');
                const titleValidation = reactionRoleValidator.validateTitle(title);
                if (!titleValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid title:** ${titleValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                config.title = title;
                await interaction.reply({
                    content: `✅ **Title set:** ${title}`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            case 'description':
                const description = interaction.fields.getTextInputValue('description');
                const descValidation = reactionRoleValidator.validateDescription(description);
                if (!descValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid description:** ${descValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                config.description = description;
                await interaction.reply({
                    content: `✅ **Description set:** ${description.length > 100 ? description.substring(0, 100) + '...' : description}`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            default:
                await interaction.reply({
                    content: '❌ Unknown modal action.',
                    flags: MessageFlags.Ephemeral
                });
                return;
        }

        // Update dashboard
        setTimeout(() => this.createDashboard(interaction, configId, true), 2000);
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (action === 'mode') {
            const mode = interaction.values[0];
            config.mode = mode;

            await interaction.reply({
                content: `✅ **Mode set:** ${mode === 'single' ? 'Single Role' : 'Multiple Roles'}`,
                flags: MessageFlags.Ephemeral
            });

            // Update dashboard
            setTimeout(() => this.createDashboard(interaction, configId, true), 2000);
        }
    },

    // Remove last role
    async removeLastRole(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.roles.length === 0) {
            return await interaction.reply({
                content: '❌ No roles to remove.',
                flags: MessageFlags.Ephemeral
            });
        }

        const removedRole = config.roles.pop();
        const role = interaction.guild.roles.cache.get(removedRole.role_id);

        await interaction.reply({
            content: `✅ **Removed role:** ${removedRole.emoji} ${removedRole.label} (${role})`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard
        setTimeout(() => this.createDashboard(interaction, configId, true), 2000);
    },

    // Show preview
    async showPreview(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Create preview panel data
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles
        };

        try {
            const { embed, components } = await reactionRoleHandler.createReactionRolePanel(channel, panelData, interaction.guild, true);

            await interaction.reply({
                content: `**🔍 Preview for ${channel}:**`,
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Preview error:', error);
            await interaction.reply({
                content: `❌ **Preview Error:** ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Create final panel
    async createPanel(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Create panel data
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles
        };

        try {
            await reactionRoleHandler.createReactionRolePanel(channel, panelData, interaction.guild);

            // Clean up configuration
            dashboardConfigs.delete(configId);

            await interaction.reply({
                content: `✅ **Reaction role panel created successfully in ${channel}!**`,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Panel creation error:', error);
            await interaction.reply({
                content: `❌ **Creation Error:** ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Cancel setup
    async cancelSetup(interaction, configId) {
        dashboardConfigs.delete(configId);

        await interaction.reply({
            content: '❌ **Setup cancelled.** Configuration has been discarded.',
            flags: MessageFlags.Ephemeral
        });
    }
};