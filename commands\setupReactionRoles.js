const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

// In-memory storage for conversation states (only for Add Role functionality)
const conversationStates = new Map();

// Cleanup old configurations every 30 minutes
setInterval(() => {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    for (const [configId, config] of dashboardConfigs.entries()) {
        if (now - config.createdAt > maxAge) {
            dashboardConfigs.delete(configId);
            console.log(`[CLEANUP] Removed expired config: ${configId}`);
        }
    }

    for (const [userId, state] of conversationStates.entries()) {
        if (now - state.createdAt > maxAge) {
            conversationStates.delete(userId);
            console.log(`[CLEANUP] Removed expired conversation state for user: ${userId}`);
        }
    }
}, 30 * 60 * 1000);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        const channel = interaction.options.getChannel('channel');

        // Validate bot permissions
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create unique configuration ID
        const configId = `${interaction.user.id}_${Date.now()}`;

        // Initialize configuration
        dashboardConfigs.set(configId, {
            userId: interaction.user.id,
            channelId: channel.id,
            title: null,
            description: null,
            mode: null,
            roles: [],
            dashboardMessageId: null,
            createdAt: Date.now()
        });

        console.log(`[DEBUG] Created config with ID: ${configId} for user: ${interaction.user.id}`);

        // Show dashboard
        await this.createDashboard(interaction, configId, false);
    },

    // Helper function to check if configuration is complete
    isConfigComplete(config) {
        return config.title && config.description && config.mode && config.roles.length > 0;
    },

    // Helper function to get hex color from input
    parseColorInput(input) {
        // Remove any whitespace
        input = input.trim();

        // If it starts with #, use as-is
        if (input.startsWith('#')) {
            return input;
        }

        // If it's just hex digits, add #
        if (/^[0-9A-Fa-f]{6}$/.test(input)) {
            return `#${input}`;
        }

        // Preset colors
        const presets = {
            'blurple': '#5865F2',
            'discord': '#5865F2',
            'red': '#ED4245',
            'green': '#57F287',
            'yellow': '#FEE75C',
            'orange': '#FF8C42',
            'purple': '#9B59B6',
            'blue': '#3498DB',
            'pink': '#E91E63',
            'black': '#2C2F33',
            'white': '#FFFFFF'
        };

        return presets[input.toLowerCase()] || null;
    },

    // Create or update dashboard display
    async createDashboard(interaction, configId, isUpdate = false) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        const isComplete = this.isConfigComplete(config);

        const embed = new EmbedBuilder()
            .setTitle('🎛️ Reaction Role Panel Setup')
            .setDescription('Configure your reaction role panel using the buttons below.')
            .setColor(config.color ? parseInt(config.color.replace('#', ''), 16) : 0x5865F2)
            .addFields(
                {
                    name: '📝 Title',
                    value: config.title || '❌ Not set',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: config.description ? (config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description) : '❌ Not set',
                    inline: true
                },
                {
                    name: '⚙️ Mode',
                    value: config.mode || '❌ Not set',
                    inline: true
                },
                {
                    name: '🎨 Color',
                    value: config.color || '❌ Default (#5865F2)',
                    inline: true
                },
                {
                    name: '🖼️ Image',
                    value: config.imageUrl ? '✅ Set' : '❌ No image',
                    inline: true
                },
                {
                    name: '📊 Channel',
                    value: channel.toString(),
                    inline: true
                },
                {
                    name: '🎭 Roles',
                    value: config.roles.length > 0 ?
                        config.roles.map((role, index) => `${index + 1}. ${role.emoji} **${role.label}** (<@&${role.role_id}>)`).join('\n') :
                        '❌ No roles added',
                    inline: false
                }
            )
            .setFooter({
                text: isComplete ? '✅ Configuration complete! You can create the panel.' : '⚠️ Complete all fields to create the panel.'
            });

        // Create action buttons
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_title_${configId}`)
                    .setLabel('Set Title')
                    .setStyle(config.title ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_description_${configId}`)
                    .setLabel('Set Description')
                    .setStyle(config.description ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_mode_${configId}`)
                    .setLabel('Set Mode')
                    .setStyle(config.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_color_${configId}`)
                    .setLabel('Set Color')
                    .setStyle(config.color ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_image_${configId}`)
                    .setLabel('Set Image')
                    .setStyle(config.imageUrl ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('🖼️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_addrole_${configId}`)
                    .setLabel('Add Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_removerole_${configId}`)
                    .setLabel('Remove Last Role')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('➖')
                    .setDisabled(config.roles.length === 0),
                new ButtonBuilder()
                    .setCustomId(`dashboard_preview_${configId}`)
                    .setLabel('Preview')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👁️')
                    .setDisabled(!isComplete)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_create_${configId}`)
                    .setLabel('Create Panel')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_cancel_${configId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const components = [row1, row2, row3];

        if (isUpdate) {
            try {
                await interaction.editReply({
                    embeds: [embed],
                    components: components
                });
            } catch (error) {
                console.error('Error updating dashboard:', error);
                const newMessage = await interaction.followUp({
                    embeds: [embed],
                    components: components,
                    flags: MessageFlags.Ephemeral
                });
                config.dashboardMessageId = newMessage.id;
            }
        } else {
            const message = await interaction.reply({
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });
            // Store the dashboard message ID for future updates
            config.dashboardMessageId = message.id;
        }
    },

    // Update existing dashboard message
    async updateDashboard(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) return;

        const channel = interaction.guild.channels.cache.get(config.channelId);
        const isComplete = config.title && config.description && config.mode && config.roles.length > 0;

        // Create updated embed
        const embed = new EmbedBuilder()
            .setTitle('🎭 Reaction Role Setup Dashboard')
            .setDescription(`**Setting up reaction roles for:** ${channel}\n\n` +
                '**Configuration Status:**')
            .addFields(
                {
                    name: '📝 Title',
                    value: config.title || '*Not set*',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: config.description ? (config.description.length > 100 ? config.description.substring(0, 100) + '...' : config.description) : '*Not set*',
                    inline: true
                },
                {
                    name: '⚙️ Mode',
                    value: config.mode ? (config.mode === 'single' ? 'Single Role' : 'Multiple Roles') : '*Not set*',
                    inline: true
                },
                {
                    name: `🎭 Roles (${config.roles.length}/25)`,
                    value: config.roles.length > 0 ?
                        config.roles.map(role => `${role.emoji} **${role.label}** (<@&${role.role_id}>)`).join('\n') :
                        '*No roles added*',
                    inline: false
                }
            )
            .setColor(isComplete ? 0x00ff00 : 0xffa500)
            .setFooter({
                text: isComplete ? 'Ready to create! Click "Create Panel" to finish.' : 'Complete all fields to create your panel.',
                iconURL: interaction.user.displayAvatarURL()
            });

        // Create updated components
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_title_${configId}`)
                    .setLabel('Set Title')
                    .setStyle(config.title ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_description_${configId}`)
                    .setLabel('Set Description')
                    .setStyle(config.description ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_mode_${configId}`)
                    .setLabel('Set Mode')
                    .setStyle(config.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('⚙️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_addrole_${configId}`)
                    .setLabel('Add Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_removerole_${configId}`)
                    .setLabel('Remove Last Role')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('➖')
                    .setDisabled(config.roles.length === 0),
                new ButtonBuilder()
                    .setCustomId(`dashboard_preview_${configId}`)
                    .setLabel('Preview')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👁️')
                    .setDisabled(!isComplete)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_create_${configId}`)
                    .setLabel('Create Panel')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_cancel_${configId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const components = [row1, row2, row3];

        // For dashboard updates, always send a new ephemeral message
        // Ephemeral messages can't be reliably edited, so we send fresh ones
        try {
            await interaction.followUp({
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Error sending dashboard update:', error);
            // Final fallback: try to send a simple message
            try {
                await interaction.followUp({
                    content: '❌ **Dashboard Update Error:** Unable to update the dashboard. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            } catch (fallbackError) {
                console.error('Even fallback failed:', fallbackError);
            }
        }
    },

    // Handle dashboard button interactions
    async handleDashboardButton(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        console.log(`[DEBUG] Dashboard button: ${action}, configId: ${configId}`);

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                await this.showTitleModal(interaction, configId);
                break;
            case 'description':
                await this.showDescriptionModal(interaction, configId);
                break;
            case 'mode':
                await this.showModeSelect(interaction, configId);
                break;
            case 'color':
                await this.showColorModal(interaction, configId);
                break;
            case 'image':
                await this.showImageModal(interaction, configId);
                break;
            case 'addrole':
                await this.startRoleConversation(interaction, configId);
                break;
            case 'removerole':
                await this.removeLastRole(interaction, configId);
                break;
            case 'preview':
                await this.showPreview(interaction, configId);
                break;
            case 'create':
                await this.createPanel(interaction, configId);
                break;
            case 'cancel':
                await this.cancelSetup(interaction, configId);
                break;
            default:
                await interaction.reply({
                    content: '❌ Unknown action.',
                    flags: MessageFlags.Ephemeral
                });
        }
    },

    // Show title modal
    async showTitleModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_title_${configId}`)
            .setTitle('Set Panel Title');

        const titleInput = new TextInputBuilder()
            .setCustomId('title')
            .setLabel('Panel Title')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the title for your reaction role panel')
            .setRequired(true)
            .setMaxLength(256);

        const firstActionRow = new ActionRowBuilder().addComponents(titleInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show description modal
    async showDescriptionModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_description_${configId}`)
            .setTitle('Set Panel Description');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Panel Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the description for your reaction role panel')
            .setRequired(true)
            .setMaxLength(4000);

        const firstActionRow = new ActionRowBuilder().addComponents(descriptionInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show color modal
    async showColorModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_color_${configId}`)
            .setTitle('Set Embed Color');

        const colorInput = new TextInputBuilder()
            .setCustomId('color')
            .setLabel('Color (Hex code or preset name)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g., #FF5733, red, blurple, discord')
            .setRequired(false)
            .setMaxLength(20);

        const firstActionRow = new ActionRowBuilder().addComponents(colorInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show image modal
    async showImageModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_image_${configId}`)
            .setTitle('Set Embed Image');

        const imageInput = new TextInputBuilder()
            .setCustomId('image')
            .setLabel('Image URL')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('https://example.com/image.png (leave empty to remove)')
            .setRequired(false)
            .setMaxLength(500);

        const firstActionRow = new ActionRowBuilder().addComponents(imageInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show mode selection
    async showModeSelect(interaction, configId) {
        const select = new StringSelectMenuBuilder()
            .setCustomId(`select_mode_${configId}`)
            .setPlaceholder('Choose reaction role mode')
            .addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('Single Role')
                    .setDescription('Users can only have one role from this panel at a time')
                    .setValue('single')
                    .setEmoji('1️⃣'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Multiple Roles')
                    .setDescription('Users can have multiple roles from this panel')
                    .setValue('multiple')
                    .setEmoji('🔢')
            );

        const row = new ActionRowBuilder().addComponents(select);

        await interaction.reply({
            content: '**Select the reaction role mode:**',
            components: [row],
            flags: MessageFlags.Ephemeral
        });
    },

    // Start conversation-based role addition
    async startRoleConversation(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Check if user already has an active conversation
        if (conversationStates.has(interaction.user.id)) {
            return await interaction.reply({
                content: '❌ **You already have an active role conversation in progress.**\n\nPlease complete your current role addition before starting a new one.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Initialize conversation state
        conversationStates.set(interaction.user.id, {
            configId: configId,
            step: 'roles',
            tempRoles: [],
            currentRoleIndex: 0,
            createdAt: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎭 Add Roles - Step 1')
            .setDescription('**Please provide your roles and emojis in this format:**\n' +
                '`@role emoji @role emoji @role emoji`\n\n' +
                '**Examples:**\n' +
                '• `@Member 🎉 @VIP ⭐ @Admin 👑`\n' +
                '• `@Red Team 🔴 @Blue Team 🔵`\n' +
                '• `@Notifications 🔔`\n\n' +
                '**Instructions:**\n' +
                '• Mention each role followed by its emoji\n' +
                '• You can add multiple role-emoji pairs in one message\n' +
                '• Use any emoji (Unicode or custom server emojis)\n' +
                '• Maximum 25 roles total per panel\n\n' +
                '**Type your roles and emojis now:**')
            .setColor(0x3498db)
            .setFooter({ text: 'You have 5 minutes to respond. Type "cancel" to abort.' });

        await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleRoleInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Restart role conversation without trying to reply (for error recovery)
    async restartRoleConversation(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return;
        }

        // Clean up any existing conversation state
        conversationStates.delete(interaction.user.id);

        // Initialize new conversation state
        conversationStates.set(interaction.user.id, {
            configId: configId,
            step: 'roles',
            tempRoles: [],
            currentRoleIndex: 0,
            createdAt: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎭 Add Roles - Step 1 (Retry)')
            .setDescription('**Please provide your roles and emojis in this format:**\n' +
                '`@role emoji @role emoji @role emoji`\n\n' +
                '**Examples:**\n' +
                '• `@Member 🎉 @VIP ⭐ @Admin 👑`\n' +
                '• `@Red Team 🔴 @Blue Team 🔵`\n' +
                '• `@Notifications 🔔`\n\n' +
                '**Instructions:**\n' +
                '• Mention each role followed by its emoji\n' +
                '• You can add multiple role-emoji pairs in one message\n' +
                '• Use any emoji (Unicode or custom server emojis)\n' +
                '• Maximum 25 roles total per panel\n\n' +
                '**Type your roles and emojis now:**')
            .setColor(0x3498db)
            .setFooter({ text: 'You have 5 minutes to respond. Type "cancel" to abort.' });

        await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleRoleInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle role input parsing for conversation
    async handleRoleInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Parse role mentions and emojis
        const roleEmojiPairs = this.parseRoleEmojiInput(message.content, interaction.guild);

        if (!roleEmojiPairs.success) {
            await interaction.followUp({
                content: `❌ **Error:** ${roleEmojiPairs.error}\n\nPlease try again with the correct format: \`@role emoji @role emoji\``,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection without trying to reply again
            setTimeout(() => this.restartRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Check total role limit
        const config = dashboardConfigs.get(state.configId);
        if (config.roles.length + roleEmojiPairs.roles.length > 25) {
            await interaction.followUp({
                content: `❌ **Too many roles:** You can only have a maximum of 25 roles per panel. You currently have ${config.roles.length} roles and are trying to add ${roleEmojiPairs.roles.length} more.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection without trying to reply again
            setTimeout(() => this.restartRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Validate roles
        const validation = reactionRoleValidator.validateRoleHierarchy(roleEmojiPairs.roles, interaction.guild.members.me);
        if (!validation.success) {
            await interaction.followUp({
                content: `❌ **Role Validation Error:** ${validation.error}\n\nPlease try again with valid roles.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection
            setTimeout(() => this.startRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Store roles and start label collection
        state.tempRoles = roleEmojiPairs.roles;
        state.step = 'labels';
        state.currentRoleIndex = 0;

        await this.startLabelCollection(interaction);
    },

    // Parse role mentions and emojis from user input
    parseRoleEmojiInput(content, guild) {
        console.log(`[DEBUG] Parsing input: "${content}"`);

        // Extract role mentions and emojis
        const rolePattern = /<@&(\d+)>/g;

        // More precise emoji pattern that handles custom Discord emojis and common Unicode emojis
        const emojiPattern = /<a?:\w+:\d+>|[\u{1F300}-\u{1F5FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F700}-\u{1F77F}]|[\u{1F780}-\u{1F7FF}]|[\u{1F800}-\u{1F8FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{1FA70}-\u{1FAFF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;

        const roleMentions = [...content.matchAll(rolePattern)];
        const emojis = [...content.matchAll(emojiPattern)];

        console.log(`[DEBUG] Found ${roleMentions.length} roles: ${roleMentions.map(m => m[0]).join(', ')}`);
        console.log(`[DEBUG] Found ${emojis.length} emojis: ${emojis.map(m => m[0]).join(', ')}`);

        if (roleMentions.length === 0) {
            return { success: false, error: 'No role mentions found. Please mention roles using @role format.' };
        }

        if (emojis.length === 0) {
            return { success: false, error: 'No emojis found. Please include emojis after each role mention.' };
        }

        if (roleMentions.length !== emojis.length) {
            return { success: false, error: `Mismatch: Found ${roleMentions.length} roles but ${emojis.length} emojis. Each role needs exactly one emoji.\n\nRoles found: ${roleMentions.map(m => m[0]).join(', ')}\nEmojis found: ${emojis.map(m => m[0]).join(', ')}` };
        }

        const roles = [];
        for (let i = 0; i < roleMentions.length; i++) {
            const roleId = roleMentions[i][1];
            const emoji = emojis[i][0];
            const role = guild.roles.cache.get(roleId);

            if (!role) {
                return { success: false, error: `Role with ID ${roleId} not found.` };
            }

            roles.push({
                role_id: roleId,
                emoji: emoji,
                label: role.name // Default label, will be customized later
            });
        }

        return { success: true, roles };
    },

    // Start label collection for each role
    async startLabelCollection(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state || state.currentRoleIndex >= state.tempRoles.length) {
            // All labels collected, add roles to config and return to dashboard
            await this.finishRoleAddition(interaction);
            return;
        }

        const currentRole = state.tempRoles[state.currentRoleIndex];
        const role = interaction.guild.roles.cache.get(currentRole.role_id);

        const embed = new EmbedBuilder()
            .setTitle(`🎭 Add Roles - Step 2 (${state.currentRoleIndex + 1}/${state.tempRoles.length})`)
            .setDescription(`**Configuring label for role:** ${role}\n\n` +
                `**Current emoji:** ${currentRole.emoji}\n` +
                `**Default label:** ${currentRole.label}\n\n` +
                `**What label would you like for the ${role.name} role?**\n\n` +
                `**Options:**\n` +
                `• Type a custom label (e.g., "VIP Member", "Team Leader")\n` +
                `• Type \`default\` to keep the current label "${currentRole.label}"\n` +
                `• Type \`cancel\` to abort the setup\n\n` +
                `**Button Style (optional):**\n` +
                `Add \`|primary\`, \`|secondary\`, \`|success\`, or \`|danger\` after your label\n` +
                `Example: \`VIP Member|danger\` or \`default|success\``)
            .setColor(0xe74c3c)
            .setFooter({ text: 'Type your choice now. You have 5 minutes to respond.' });

        await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector for label
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleLabelInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle label input
    async handleLabelInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Handle label input with optional button style
        const input = message.content.trim();
        const inputLower = input.toLowerCase();

        // Parse label and button style
        let label = input;
        let buttonStyle = null;

        if (input.includes('|')) {
            const parts = input.split('|');
            label = parts[0].trim();
            const styleInput = parts[1].trim().toLowerCase();

            // Map style names to ButtonStyle values
            const { ButtonStyle } = require('discord.js');
            const styleMap = {
                'primary': ButtonStyle.Primary,
                'secondary': ButtonStyle.Secondary,
                'success': ButtonStyle.Success,
                'danger': ButtonStyle.Danger
            };

            if (styleMap[styleInput]) {
                buttonStyle = styleMap[styleInput];
            }
        }

        if (inputLower === 'default' || label.toLowerCase() === 'default') {
            // Keep the default label (already set)
            if (buttonStyle) {
                state.tempRoles[state.currentRoleIndex].buttonStyle = buttonStyle;
            }
            await interaction.followUp({
                content: `✅ **Keeping default label:** "${state.tempRoles[state.currentRoleIndex].label}"${buttonStyle ? ` with ${Object.keys(require('discord.js').ButtonStyle).find(key => require('discord.js').ButtonStyle[key] === buttonStyle).toLowerCase()} style` : ''}`,
                flags: MessageFlags.Ephemeral
            });
        } else if (label && label.length > 0) {
            // Custom label provided - validate it
            const labelValidation = reactionRoleValidator.validateTitle(label);
            if (!labelValidation.success) {
                await interaction.followUp({
                    content: `❌ **Invalid label:** ${labelValidation.error}\n\nPlease try again with a valid label or type \`default\` to keep the current label.`,
                    flags: MessageFlags.Ephemeral
                });

                // Restart label collection for current role
                setTimeout(() => this.startLabelCollection(interaction), 2000);
                return;
            }

            // Set custom label and button style
            state.tempRoles[state.currentRoleIndex].label = label;
            if (buttonStyle) {
                state.tempRoles[state.currentRoleIndex].buttonStyle = buttonStyle;
            }
            await interaction.followUp({
                content: `✅ **Custom label set:** "${label}"${buttonStyle ? ` with ${Object.keys(require('discord.js').ButtonStyle).find(key => require('discord.js').ButtonStyle[key] === buttonStyle).toLowerCase()} style` : ''}`,
                flags: MessageFlags.Ephemeral
            });
        } else {
            // Empty input - ask again
            await interaction.followUp({
                content: `❌ **Please provide a label.** Type a custom label or type \`default\` to keep "${state.tempRoles[state.currentRoleIndex].label}".`,
                flags: MessageFlags.Ephemeral
            });

            // Restart label collection for current role
            setTimeout(() => this.startLabelCollection(interaction), 2000);
            return;
        }

        // Move to next role
        state.currentRoleIndex++;
        await this.startLabelCollection(interaction);
    },

    // Finish role addition and return to dashboard
    async finishRoleAddition(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        const config = dashboardConfigs.get(state.configId);
        if (!config) {
            conversationStates.delete(interaction.user.id);
            return await interaction.followUp({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Add new roles to config
        config.roles.push(...state.tempRoles);

        // Clean up conversation state
        conversationStates.delete(interaction.user.id);

        // Show success message
        await interaction.followUp({
            content: `✅ **Successfully added ${state.tempRoles.length} role(s) to your panel!**\n\nReturning to dashboard...`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard
        setTimeout(() => this.updateDashboard(interaction, state.configId), 1000);
    },

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                const title = interaction.fields.getTextInputValue('title');
                const titleValidation = reactionRoleValidator.validateTitle(title);
                if (!titleValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid title:** ${titleValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                config.title = title;
                config.title = title;
                await interaction.reply({
                    content: `✅ **Title set:** ${title}`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            case 'description':
                const description = interaction.fields.getTextInputValue('description');
                const descValidation = reactionRoleValidator.validateDescription(description);
                if (!descValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid description:** ${descValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                config.description = description;
                await interaction.reply({
                    content: `✅ **Description set:** ${description.length > 100 ? description.substring(0, 100) + '...' : description}`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            case 'color':
                const colorInput = interaction.fields.getTextInputValue('color');
                if (colorInput.trim() === '') {
                    // Remove color (use default)
                    config.color = null;
                    await interaction.reply({
                        content: '✅ **Color reset to default:** #5865F2',
                        flags: MessageFlags.Ephemeral
                    });
                } else {
                    const parsedColor = this.parseColorInput(colorInput);
                    if (!parsedColor) {
                        return await interaction.reply({
                            content: `❌ **Invalid color:** "${colorInput}"\n\nValid formats:\n• Hex codes: #FF5733 or FF5733\n• Preset names: red, green, blue, blurple, discord, etc.`,
                            flags: MessageFlags.Ephemeral
                        });
                    }
                    config.color = parsedColor;
                    await interaction.reply({
                        content: `✅ **Color set:** ${parsedColor}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            case 'image':
                const imageInput = interaction.fields.getTextInputValue('image');
                if (imageInput.trim() === '') {
                    // Remove image
                    config.imageUrl = null;
                    await interaction.reply({
                        content: '✅ **Image removed**',
                        flags: MessageFlags.Ephemeral
                    });
                } else {
                    const imageValidation = reactionRoleValidator.validateImageUrl(imageInput);
                    if (!imageValidation.success) {
                        return await interaction.reply({
                            content: `❌ **Invalid image URL:** ${imageValidation.error}`,
                            flags: MessageFlags.Ephemeral
                        });
                    }
                    config.imageUrl = imageInput;
                    await interaction.reply({
                        content: `✅ **Image set:** ${imageInput}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            default:
                await interaction.reply({
                    content: '❌ Unknown modal action.',
                    flags: MessageFlags.Ephemeral
                });
                return;
        }

        // Update dashboard
        setTimeout(() => this.updateDashboard(interaction, configId), 1000);
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (action === 'mode') {
            const mode = interaction.values[0];
            config.mode = mode;

            await interaction.reply({
                content: `✅ **Mode set:** ${mode === 'single' ? 'Single Role' : 'Multiple Roles'}`,
                flags: MessageFlags.Ephemeral
            });

            // Update dashboard
            setTimeout(() => this.updateDashboard(interaction, configId), 1000);
        }
    },

    // Remove last role
    async removeLastRole(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.roles.length === 0) {
            return await interaction.reply({
                content: '❌ No roles to remove.',
                flags: MessageFlags.Ephemeral
            });
        }

        const removedRole = config.roles.pop();
        const role = interaction.guild.roles.cache.get(removedRole.role_id);

        await interaction.reply({
            content: `✅ **Removed role:** ${removedRole.emoji} ${removedRole.label} (${role})`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard
        setTimeout(() => this.updateDashboard(interaction, configId), 1000);
    },

    // Show preview
    async showPreview(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate configuration completeness
        if (!this.isConfigComplete(config)) {
            return await interaction.reply({
                content: '❌ **Cannot preview incomplete configuration.**\n\nPlease ensure you have set:\n• Title\n• Description\n• Mode\n• At least one role',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Validate roles before preview
        const roleValidation = await reactionRoleValidator.validateRoleHierarchy(config.roles, interaction.guild.members.me);
        if (!roleValidation.success) {
            return await interaction.reply({
                content: `❌ **Role Validation Error:** ${roleValidation.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            // Create preview embed and components manually
            const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            const embed = new EmbedBuilder()
                .setTitle(config.title)
                .setDescription(config.description)
                .setColor(config.color ? parseInt(config.color.replace('#', ''), 16) : 0x5865F2)
                .setFooter({
                    text: `Mode: ${config.mode === 'single' ? 'Single Role (selecting one removes others)' : 'Multiple Roles (toggle on/off)'}`,
                    iconURL: interaction.guild.iconURL()
                });

            // Add image if set
            if (config.imageUrl) {
                embed.setImage(config.imageUrl);
            }

            // Create preview buttons (disabled for preview)
            const components = [];
            const roles = config.roles;

            for (let i = 0; i < roles.length; i += 5) {
                const row = new ActionRowBuilder();
                const rowRoles = roles.slice(i, i + 5);

                for (const role of rowRoles) {
                    const guildRole = interaction.guild.roles.cache.get(role.role_id);
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId(`preview_${role.role_id}`)
                            .setLabel(`${role.emoji} ${role.label}`)
                            .setStyle(role.buttonStyle || ButtonStyle.Secondary)
                            .setDisabled(true) // Disabled for preview
                    );
                }

                components.push(row);
            }

            await interaction.reply({
                content: `**🔍 Preview for ${channel}:**\n*This is how your reaction role panel will look. Buttons are disabled in preview mode.*`,
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Preview error:', error);
            await interaction.reply({
                content: `❌ **Preview Error:** ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Create final panel
    async createPanel(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate configuration completeness
        if (!this.isConfigComplete(config)) {
            return await interaction.reply({
                content: '❌ **Cannot create incomplete panel.**\n\nPlease ensure you have set:\n• Title\n• Description\n• Mode\n• At least one role',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Validate bot permissions in target channel
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error in ${channel}:**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate role hierarchy
        const roleValidation = await reactionRoleValidator.validateRoleHierarchy(config.roles, botMember);
        if (!roleValidation.success) {
            return await interaction.reply({
                content: `❌ **Role Validation Error:** ${roleValidation.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create panel data
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles,
            color: config.color,
            imageUrl: config.imageUrl
        };

        try {
            // Show creating message
            await interaction.reply({
                content: `🔄 **Creating reaction role panel in ${channel}...**`,
                flags: MessageFlags.Ephemeral
            });

            // Create the panel
            const result = await reactionRoleHandler.createReactionRolePanel(channel, panelData, interaction.guild);

            if (result.success) {
                // Clean up configuration
                dashboardConfigs.delete(configId);

                // Update with success message
                await interaction.editReply({
                    content: `✅ **Reaction role panel created successfully in ${channel}!**\n\n` +
                        `**Panel Details:**\n` +
                        `• **Title:** ${config.title}\n` +
                        `• **Mode:** ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}\n` +
                        `• **Roles:** ${config.roles.length} role(s) configured\n` +
                        `• **Message ID:** ${result.messageId}\n\n` +
                        `Users can now click the buttons to get or remove roles!`
                });
            } else {
                await interaction.editReply({
                    content: `❌ **Panel Creation Failed:** ${result.error}`
                });
            }

        } catch (error) {
            console.error('Panel creation error:', error);

            try {
                await interaction.editReply({
                    content: `❌ **Creation Error:** ${error.message}`
                });
            } catch (editError) {
                console.error('Error editing reply:', editError);
                await interaction.followUp({
                    content: `❌ **Creation Error:** ${error.message}`,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Cancel setup
    async cancelSetup(interaction, configId) {
        dashboardConfigs.delete(configId);

        await interaction.reply({
            content: '❌ **Setup cancelled.** Configuration has been discarded.',
            flags: MessageFlags.Ephemeral
        });
    }
};