const {
    <PERSON><PERSON><PERSON>ommandB<PERSON>er,
    PermissionFlagsBits,
    ChannelType,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    MessageFlags
} = require('discord.js');
const reactionRoleStorage = require('../utils/reactionRoleStorage');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive setup wizard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        try {
            // Check if user already has an active setup session
            const existingSession = setupWizardState.getSession(interaction.user.id);
            if (existingSession) {
                return interaction.reply({
                    content: '⚠️ You already have an active setup session. Please complete or cancel it first.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Start new setup session
            const session = setupWizardState.startSession(interaction.user.id, interaction.guild.id);

            // Create and send the first step (channel selection)
            const stepContent = setupWizardUI.createChannelSelectionStep(interaction.guild, session);

            const response = await interaction.reply({
                ...stepContent,
                flags: MessageFlags.Ephemeral
            });

            // Store the message ID for future updates
            setupWizardState.updateSession(interaction.user.id, {
                messageId: response.id
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);

            // Clean up session on error
            setupWizardState.endSession(interaction.user.id);

            const errorMessage = setupWizardUI.createErrorMessage(error.message);

            if (interaction.replied || interaction.deferred) {
                return interaction.editReply(errorMessage);
            } else {
                return interaction.reply({
                    ...errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    }
};
