const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags
} = require('discord.js');
const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText))
        .addStringOption(option =>
            option.setName('title')
                .setDescription('Title for the reaction role panel')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Description for the reaction role panel')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('mode')
                .setDescription('Role assignment mode')
                .setRequired(true)
                .addChoices(
                    { name: 'Single Role (users can only have one role from this panel)', value: 'single' },
                    { name: 'Multiple Roles (users can have multiple roles)', value: 'multi' }
                ))
        .addRoleOption(option =>
            option.setName('role1')
                .setDescription('First role')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('emoji1')
                .setDescription('Emoji for first role')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('label1')
                .setDescription('Button label for first role')
                .setRequired(true))
        .addRoleOption(option =>
            option.setName('role2')
                .setDescription('Second role (optional)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('emoji2')
                .setDescription('Emoji for second role')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('label2')
                .setDescription('Button label for second role')
                .setRequired(false))
        .addRoleOption(option =>
            option.setName('role3')
                .setDescription('Third role (optional)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('emoji3')
                .setDescription('Emoji for third role')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('label3')
                .setDescription('Button label for third role')
                .setRequired(false))
        .addRoleOption(option =>
            option.setName('role4')
                .setDescription('Fourth role (optional)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('emoji4')
                .setDescription('Emoji for fourth role')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('label4')
                .setDescription('Button label for fourth role')
                .setRequired(false))
        .addRoleOption(option =>
            option.setName('role5')
                .setDescription('Fifth role (optional)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('emoji5')
                .setDescription('Emoji for fifth role')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('label5')
                .setDescription('Button label for fifth role')
                .setRequired(false)),

    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('channel');
            const title = interaction.options.getString('title');
            const description = interaction.options.getString('description');
            const mode = interaction.options.getString('mode');

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check channel permissions
            const channelPermissions = channel.permissionsFor(botMember);
            if (!channelPermissions.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.EmbedLinks])) {
                return interaction.reply({
                    content: `❌ I don't have permission to send messages and embeds in ${channel}.`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Collect roles and validate them
            const roles = [];
            for (let i = 1; i <= 5; i++) {
                const role = interaction.options.getRole(`role${i}`);
                const emoji = interaction.options.getString(`emoji${i}`);
                const label = interaction.options.getString(`label${i}`);

                if (role) {
                    // Validate role
                    if (role.position >= botMember.roles.highest.position) {
                        return interaction.reply({
                            content: `❌ I cannot manage the role "${role.name}" because it's higher than or equal to my highest role.`,
                            flags: MessageFlags.Ephemeral
                        });
                    }

                    if (role.managed) {
                        return interaction.reply({
                            content: `❌ The role "${role.name}" is managed by an integration and cannot be assigned.`,
                            flags: MessageFlags.Ephemeral
                        });
                    }

                    if (!emoji || !label) {
                        return interaction.reply({
                            content: `❌ Role "${role.name}" is missing emoji or label. Please provide both.`,
                            flags: MessageFlags.Ephemeral
                        });
                    }

                    roles.push({
                        role_id: role.id,
                        emoji: emoji,
                        label: label
                    });
                }
            }

            if (roles.length === 0) {
                return interaction.reply({
                    content: '❌ You must provide at least one role.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Create panel data
            const panelData = {
                title: title,
                description: description,
                mode: mode,
                roles: roles
            };

            // Create the reaction role panel
            const result = await reactionRoleHandler.createReactionRolePanel(
                channel,
                panelData,
                interaction.guild
            );

            if (result.success) {
                await interaction.reply({
                    content: `✅ **Reaction role panel created successfully!**\n\n` +
                            `📍 **Channel:** ${channel}\n` +
                            `📝 **Title:** ${title}\n` +
                            `⚙️ **Mode:** ${mode === 'single' ? 'Single Role' : 'Multiple Roles'}\n` +
                            `🎭 **Roles:** ${roles.length} role(s)\n` +
                            `🆔 **Panel ID:** \`${result.panelId}\``,
                    flags: MessageFlags.Ephemeral
                });
            } else {
                await interaction.reply({
                    content: `❌ Failed to create reaction role panel: ${result.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);

            const errorMessage = `❌ An error occurred while creating the reaction role panel: ${error.message}`;

            if (interaction.replied || interaction.deferred) {
                return interaction.editReply({ content: errorMessage });
            } else {
                return interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    }
};
