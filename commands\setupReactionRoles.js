const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ChannelType
} = require('discord.js');
const reactionRoleStorage = require('../utils/reactionRoleStorage');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel with buttons')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText))
        .addStringOption(option =>
            option.setName('title')
                .setDescription('Title for the embed (max 256 characters)')
                .setRequired(true)
                .setMaxLength(256))
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Description for the embed (max 4096 characters)')
                .setRequired(true)
                .setMaxLength(4096))
        .addStringOption(option =>
            option.setName('roles')
                .setDescription('Role-emoji-label pairs: role_id:emoji:label,role_id:emoji:label')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('mode')
                .setDescription('Role assignment mode')
                .setRequired(true)
                .addChoices(
                    { name: 'Multiple roles (users can have multiple roles)', value: 'multi' },
                    { name: 'Single role (users can only have one role)', value: 'single' }
                ))
        .addStringOption(option =>
            option.setName('image_url')
                .setDescription('Optional image URL for the embed')
                .setRequired(false)),

    async execute(interaction) {
        try {
            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const channel = interaction.options.getChannel('channel');

            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            const title = interaction.options.getString('title');
            const description = interaction.options.getString('description');
            const rolesString = interaction.options.getString('roles');
            const mode = interaction.options.getString('mode');
            const imageUrl = interaction.options.getString('image_url');

            // Validate inputs using the validator
            const titleValidation = reactionRoleValidator.validateTitle(title);
            if (!titleValidation.success) {
                return interaction.reply({
                    content: `❌ ${titleValidation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            const descriptionValidation = reactionRoleValidator.validateDescription(description);
            if (!descriptionValidation.success) {
                return interaction.reply({
                    content: `❌ ${descriptionValidation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            const rolesValidation = reactionRoleValidator.validateRolesString(rolesString, interaction.guild);
            if (!rolesValidation.success) {
                return interaction.reply({
                    content: `❌ ${rolesValidation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            const imageValidation = reactionRoleValidator.validateImageUrl(imageUrl);
            if (!imageValidation.success) {
                return interaction.reply({
                    content: `❌ ${imageValidation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Parse roles string
            const roleConfigs = await this.parseRolesString(rolesString, interaction.guild);
            if (!roleConfigs.success) {
                return interaction.reply({
                    content: `❌ ${roleConfigs.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Validate role hierarchy
            const hierarchyCheck = reactionRoleValidator.validateRoleHierarchy(roleConfigs.roles, botMember);
            if (!hierarchyCheck.success) {
                return interaction.reply({
                    content: `❌ ${hierarchyCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            // Create embed
            const embed = new EmbedBuilder()
                .setTitle(title)
                .setDescription(description)
                .setColor(0x5865F2);

            if (imageUrl) {
                embed.setImage(imageUrl);
            }

            // Create buttons
            const buttons = this.createRoleButtons(roleConfigs.roles);
            const actionRows = this.createActionRows(buttons);

            // Send the panel
            const panelMessage = await channel.send({
                embeds: [embed],
                components: actionRows
            });

            // Generate panel ID and store configuration
            const panelId = reactionRoleStorage.generatePanelId();
            const panelData = {
                guild_id: interaction.guild.id,
                channel_id: channel.id,
                message_id: panelMessage.id,
                title: title,
                description: description,
                image_url: imageUrl,
                mode: mode,
                roles: roleConfigs.roles
            };

            await reactionRoleStorage.addPanel(panelId, panelData);

            await interaction.editReply({
                content: `✅ Reaction role panel created successfully in ${channel}!\n\n**Panel ID:** \`${panelId}\`\n**Mode:** ${mode === 'multi' ? 'Multiple roles' : 'Single role'}\n**Roles:** ${roleConfigs.roles.length}`
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);
            
            const errorMessage = `❌ An error occurred while creating the reaction role panel: ${error.message}`;
            
            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    /**
     * Parse the roles string into role configurations
     * @param {string} rolesString - The roles string to parse
     * @param {Guild} guild - The Discord guild
     * @returns {Promise<Object>} Parse result with success status and data/error
     */
    async parseRolesString(rolesString, guild) {
        try {
            const rolePairs = rolesString.split(',').map(pair => pair.trim());
            const roles = [];

            for (const pair of rolePairs) {
                const parts = pair.split(':');
                if (parts.length !== 3) {
                    return {
                        success: false,
                        error: `Invalid format for role pair: "${pair}". Expected format: role_id:emoji:label`
                    };
                }

                const [roleId, emoji, label] = parts.map(part => part.trim());

                // Validate role exists
                const role = guild.roles.cache.get(roleId);
                if (!role) {
                    return {
                        success: false,
                        error: `Role with ID "${roleId}" not found in this server.`
                    };
                }

                // Validate emoji format
                const emojiValidation = reactionRoleValidator.validateEmoji(emoji);
                if (!emojiValidation.success) {
                    return {
                        success: false,
                        error: emojiValidation.error
                    };
                }

                // Validate label
                if (!label || label.length > 80) {
                    return {
                        success: false,
                        error: `Invalid label: "${label}". Label must be 1-80 characters long.`
                    };
                }

                roles.push({
                    role_id: roleId,
                    emoji: emoji,
                    label: label
                });
            }

            if (roles.length === 0) {
                return {
                    success: false,
                    error: 'At least one role must be specified.'
                };
            }

            if (roles.length > 25) {
                return {
                    success: false,
                    error: 'Maximum of 25 roles allowed per panel (Discord button limit).'
                };
            }

            return {
                success: true,
                roles: roles
            };

        } catch (error) {
            return {
                success: false,
                error: `Error parsing roles: ${error.message}`
            };
        }
    },



    /**
     * Create role buttons
     * @param {Array} roles - Array of role configurations
     * @returns {Array} Array of button builders
     */
    createRoleButtons(roles) {
        return roles.map(role => {
            return new ButtonBuilder()
                .setCustomId(`reaction_role_${role.role_id}`)
                .setLabel(`${role.emoji} ${role.label}`)
                .setStyle(ButtonStyle.Secondary);
        });
    },

    /**
     * Create action rows for buttons (max 5 buttons per row)
     * @param {Array} buttons - Array of button builders
     * @returns {Array} Array of action row builders
     */
    createActionRows(buttons) {
        const actionRows = [];
        
        for (let i = 0; i < buttons.length; i += 5) {
            const row = new ActionRowBuilder();
            const rowButtons = buttons.slice(i, i + 5);
            row.addComponents(rowButtons);
            actionRows.push(row);
        }
        
        return actionRows;
    }
};
