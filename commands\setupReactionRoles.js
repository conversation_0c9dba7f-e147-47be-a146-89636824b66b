const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        const channel = interaction.options.getChannel('channel');

        // Validate bot permissions
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create unique configuration ID
        const configId = `${interaction.user.id}_${Date.now()}`;

        // Initialize configuration
        dashboardConfigs.set(configId, {
            userId: interaction.user.id,
            channelId: channel.id,
            title: null,
            description: null,
            mode: null,
            roles: [],
            createdAt: Date.now()
        });

        // Show dashboard
        await this.createDashboard(interaction, configId, false);
    },

    // Helper function to check if configuration is complete
    isConfigComplete(config) {
        return config.title && config.description && config.mode && config.roles.length > 0;
    },

    // Create or update dashboard display
    async createDashboard(interaction, configId, isUpdate = false) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        const isComplete = this.isConfigComplete(config);

        const embed = new EmbedBuilder()
            .setTitle('🎛️ Reaction Role Panel Setup')
            .setDescription('Configure your reaction role panel using the buttons below.')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '📝 Title',
                    value: config.title || '❌ Not set',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: config.description ? (config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description) : '❌ Not set',
                    inline: true
                },
                {
                    name: '⚙️ Mode',
                    value: config.mode || '❌ Not set',
                    inline: true
                },
                {
                    name: '🎭 Roles',
                    value: config.roles.length > 0 ? `${config.roles.length} role(s) configured` : '❌ No roles added',
                    inline: false
                }
            )
            .setFooter({
                text: isComplete ? '✅ Configuration complete! You can create the panel.' : '⚠️ Complete all fields to create the panel.'
            });

        // Create action buttons
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_title_${configId}`)
                    .setLabel('Set Title')
                    .setStyle(config.title ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_description_${configId}`)
                    .setLabel('Set Description')
                    .setStyle(config.description ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_mode_${configId}`)
                    .setLabel('Set Mode')
                    .setStyle(config.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('⚙️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_addrole_${configId}`)
                    .setLabel('Add Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_removerole_${configId}`)
                    .setLabel('Remove Last Role')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('➖')
                    .setDisabled(config.roles.length === 0),
                new ButtonBuilder()
                    .setCustomId(`dashboard_preview_${configId}`)
                    .setLabel('Preview')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👁️')
                    .setDisabled(!isComplete)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_create_${configId}`)
                    .setLabel('Create Panel')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_cancel_${configId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const messageOptions = {
            embeds: [embed],
            components: [row1, row2, row3],
            flags: MessageFlags.Ephemeral
        };

        if (isUpdate) {
            return await interaction.update(messageOptions);
        } else {
            return await interaction.reply(messageOptions);
        }
    },

    // Handle dashboard button interactions
    async handleDashboardButton(interaction) {
        const [action, configId] = interaction.customId.split('_').slice(1);
        const config = dashboardConfigs.get(configId);

        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ You can only modify your own configuration.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                await this.showTitleModal(interaction, configId);
                break;
            case 'description':
                await this.showDescriptionModal(interaction, configId);
                break;
            case 'mode':
                await this.showModeSelect(interaction, configId);
                break;
            case 'addrole':
                await this.showAddRoleModal(interaction, configId);
                break;
            case 'removerole':
                await this.removeLastRole(interaction, configId);
                break;
            case 'preview':
                await this.showPreview(interaction, configId);
                break;
            case 'create':
                await this.createPanel(interaction, configId);
                break;
            case 'cancel':
                await this.cancelDashboard(interaction, configId);
                break;
            default:
                await interaction.reply({
                    content: '❌ Unknown action.',
                    flags: MessageFlags.Ephemeral
                });
        }
    },

    // Show title input modal
    async showTitleModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_title_${configId}`)
            .setTitle('Set Panel Title');

        const titleInput = new TextInputBuilder()
            .setCustomId('title')
            .setLabel('Panel Title')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the title for your reaction role panel')
            .setRequired(true)
            .setMaxLength(100);

        const actionRow = new ActionRowBuilder().addComponents(titleInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    },

    // Show description input modal
    async showDescriptionModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_description_${configId}`)
            .setTitle('Set Panel Description');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Panel Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the description for your reaction role panel')
            .setRequired(true)
            .setMaxLength(1000);

        const actionRow = new ActionRowBuilder().addComponents(descriptionInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    },

    // Show mode selection dropdown
    async showModeSelect(interaction, configId) {
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`select_mode_${configId}`)
            .setPlaceholder('Choose reaction role mode')
            .addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('Single Role')
                    .setDescription('Users can only have one role from this panel at a time')
                    .setValue('single')
                    .setEmoji('1️⃣'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Multi Role')
                    .setDescription('Users can have multiple roles from this panel')
                    .setValue('multi')
                    .setEmoji('🔢')
            );

        const actionRow = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            content: '**Select the reaction role mode:**',
            components: [actionRow],
            flags: MessageFlags.Ephemeral
        });
    },

    // Show add role modal
    async showAddRoleModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_addrole_${configId}`)
            .setTitle('Add Role');

        const roleInput = new TextInputBuilder()
            .setCustomId('role_id')
            .setLabel('Role ID')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the role ID (e.g., 123456789012345678)')
            .setRequired(true);

        const emojiInput = new TextInputBuilder()
            .setCustomId('emoji')
            .setLabel('Emoji')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter emoji (e.g., 😀 or :custom_emoji:)')
            .setRequired(true);

        const labelInput = new TextInputBuilder()
            .setCustomId('label')
            .setLabel('Label')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a label for this role')
            .setRequired(true)
            .setMaxLength(50);

        const row1 = new ActionRowBuilder().addComponents(roleInput);
        const row2 = new ActionRowBuilder().addComponents(emojiInput);
        const row3 = new ActionRowBuilder().addComponents(labelInput);

        modal.addComponents(row1, row2, row3);

        await interaction.showModal(modal);
    },

    // Remove the last added role
    async removeLastRole(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (config.roles.length > 0) {
            const removedRole = config.roles.pop();
            await interaction.reply({
                content: `✅ Removed role: ${removedRole.label}`,
                flags: MessageFlags.Ephemeral
            });

            // Update dashboard after a short delay
            setTimeout(async () => {
                try {
                    await this.createDashboard(interaction, configId, true);
                } catch (error) {
                    console.error('Error updating dashboard after role removal:', error);
                }
            }, 1000);
        } else {
            await interaction.reply({
                content: '❌ No roles to remove.',
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Show preview of the panel
    async showPreview(interaction, configId) {
        const config = dashboardConfigs.get(configId);

        const previewEmbed = new EmbedBuilder()
            .setTitle(config.title)
            .setDescription(config.description)
            .setColor(0x5865F2)
            .addFields({
                name: 'Available Roles',
                value: config.roles.map(role => `${role.emoji} ${role.label}`).join('\n'),
                inline: false
            })
            .setFooter({
                text: `Mode: ${config.mode === 'single' ? 'Single Role' : 'Multi Role'} | ${config.roles.length} role(s)`
            });

        await interaction.reply({
            content: '👁️ **Panel Preview:**',
            embeds: [previewEmbed],
            flags: MessageFlags.Ephemeral
        });
    },

    // Create the actual reaction role panel
    async createPanel(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        const channel = interaction.guild.channels.cache.get(config.channelId);

        if (!channel) {
            return await interaction.reply({
                content: '❌ Channel not found.',
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            // Prepare panel data for the reaction role handler
            const panelData = {
                title: config.title,
                description: config.description,
                mode: config.mode,
                roles: config.roles.map(role => ({
                    role_id: role.role_id,
                    emoji: role.emoji,
                    label: role.label
                }))
            };

            // Create the panel using the existing reaction role handler
            const success = await reactionRoleHandler.createReactionRolePanel(channel, panelData, interaction.guild);

            if (success) {
                // Clean up configuration
                dashboardConfigs.delete(configId);

                await interaction.reply({
                    content: `✅ **Reaction role panel created successfully!**\n\nPanel has been posted in ${channel}`,
                    flags: MessageFlags.Ephemeral
                });
            } else {
                await interaction.reply({
                    content: '❌ Failed to create reaction role panel. Please try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        } catch (error) {
            console.error('Error creating reaction role panel:', error);
            await interaction.reply({
                content: '❌ An error occurred while creating the panel. Please try again.',
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Cancel dashboard and clean up
    async cancelDashboard(interaction, configId) {
        dashboardConfigs.delete(configId);

        await interaction.update({
            content: '❌ **Setup cancelled.** Configuration has been discarded.',
            embeds: [],
            components: []
        });
    },

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        const [action, configId] = interaction.customId.split('_').slice(1);
        const config = dashboardConfigs.get(configId);

        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ You can only modify your own configuration.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                const title = interaction.fields.getTextInputValue('title');
                config.title = title;
                await interaction.reply({
                    content: `✅ Title set to: **${title}**`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            case 'description':
                const description = interaction.fields.getTextInputValue('description');
                config.description = description;
                await interaction.reply({
                    content: `✅ Description set successfully.`,
                    flags: MessageFlags.Ephemeral
                });
                break;

            case 'addrole':
                await this.handleAddRoleModal(interaction, configId);
                return;

            default:
                await interaction.reply({
                    content: '❌ Unknown modal action.',
                    flags: MessageFlags.Ephemeral
                });
                return;
        }

        // Update dashboard after a short delay
        setTimeout(async () => {
            try {
                await this.createDashboard(interaction, configId, true);
            } catch (error) {
                console.error('Error updating dashboard after modal:', error);
            }
        }, 1000);
    },

    // Handle add role modal specifically
    async handleAddRoleModal(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        const roleId = interaction.fields.getTextInputValue('role_id');
        const emoji = interaction.fields.getTextInputValue('emoji');
        const label = interaction.fields.getTextInputValue('label');

        // Validate role
        const role = interaction.guild.roles.cache.get(roleId);
        if (!role) {
            return await interaction.reply({
                content: '❌ Invalid role ID. Please make sure the role exists.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate role permissions
        const roleValidation = await reactionRoleValidator.validateRole(role, interaction.guild);
        if (!roleValidation.valid) {
            return await interaction.reply({
                content: `❌ **Role Validation Error**\n${roleValidation.message}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Check if role already exists in configuration
        if (config.roles.some(r => r.role_id === roleId)) {
            return await interaction.reply({
                content: '❌ This role is already added to the panel.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Add role to configuration
        config.roles.push({
            role_id: roleId,
            emoji: emoji,
            label: label
        });

        await interaction.reply({
            content: `✅ Added role: ${emoji} **${label}** (${role.name})`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard after a short delay
        setTimeout(async () => {
            try {
                await this.createDashboard(interaction, configId, true);
            } catch (error) {
                console.error('Error updating dashboard after adding role:', error);
            }
        }, 1000);
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        const [action, configId] = interaction.customId.split('_').slice(1);
        const config = dashboardConfigs.get(configId);

        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ You can only modify your own configuration.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (action === 'mode') {
            const selectedMode = interaction.values[0];
            config.mode = selectedMode;

            const modeText = selectedMode === 'single' ? 'Single Role' : 'Multi Role';
            await interaction.update({
                content: `✅ Mode set to: **${modeText}**`,
                components: []
            });

            // Update dashboard after a short delay
            setTimeout(async () => {
                try {
                    await this.createDashboard(interaction, configId, true);
                } catch (error) {
                    console.error('Error updating dashboard after mode selection:', error);
                }
            }, 1000);
        } else {
            await interaction.reply({
                content: '❌ Unknown select action.',
                flags: MessageFlags.Ephemeral
            });
        }
    }
};
