const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle
} = require('discord.js');
const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('channel');

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check channel permissions
            const channelPermissions = channel.permissionsFor(botMember);
            if (!channelPermissions.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.EmbedLinks])) {
                return interaction.reply({
                    content: `❌ I don't have permission to send messages and embeds in ${channel}.`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Initialize dashboard configuration
            const configId = `${interaction.user.id}_${Date.now()}`;
            dashboardConfigs.set(configId, {
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                channel: channel,
                title: null,
                description: null,
                mode: null,
                roles: []
            });

            // Create and send dashboard
            const dashboardContent = createDashboard(configId);
            await interaction.reply({
                ...dashboardContent,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);

            const errorMessage = `❌ An error occurred while creating the dashboard: ${error.message}`;

            if (interaction.replied || interaction.deferred) {
                return interaction.editReply({ content: errorMessage });
            } else {
                return interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Handle button interactions for the dashboard
    async handleDashboardButton(interaction) {
        try {
            const customId = interaction.customId;
            const [action, configId] = customId.split('_').slice(1); // Remove 'dashboard' prefix

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            if (config.userId !== interaction.user.id) {
                return interaction.reply({
                    content: '❌ You can only interact with your own dashboard.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'title':
                    await showTitleModal(interaction, configId);
                    break;
                case 'description':
                    await showDescriptionModal(interaction, configId);
                    break;
                case 'mode':
                    await showModeSelect(interaction, configId);
                    break;
                case 'roles':
                    await showRoleSelect(interaction, configId);
                    break;
                case 'preview':
                    await showPreview(interaction, configId);
                    break;
                case 'create':
                    await createPanel(interaction, configId);
                    break;
                case 'cancel':
                    await cancelDashboard(interaction, configId);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('Error handling dashboard button:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        try {
            const customId = interaction.customId;
            const [action, configId] = customId.split('_').slice(1); // Remove 'modal' prefix

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'title':
                    const title = interaction.fields.getTextInputValue('title_input');
                    config.title = title;
                    dashboardConfigs.set(configId, config);

                    const titleDashboard = createDashboard(configId);
                    await interaction.update(titleDashboard);
                    break;

                case 'description':
                    const description = interaction.fields.getTextInputValue('description_input');
                    config.description = description;
                    dashboardConfigs.set(configId, config);

                    const descDashboard = createDashboard(configId);
                    await interaction.update(descDashboard);
                    break;

                default:
                    await interaction.reply({
                        content: '❌ Unknown modal action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('Error handling dashboard modal:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        try {
            const customId = interaction.customId;
            const [action, configId] = customId.split('_').slice(1); // Remove 'select' prefix

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'mode':
                    const mode = interaction.values[0];
                    config.mode = mode;
                    dashboardConfigs.set(configId, config);

                    const modeDashboard = createDashboard(configId);
                    await interaction.update(modeDashboard);
                    break;

                default:
                    await interaction.reply({
                        content: '❌ Unknown select action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('Error handling dashboard select:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }
};

// Helper function to create the dashboard embed and buttons
function createDashboard(configId) {
    const config = dashboardConfigs.get(configId);
    if (!config) return null;

    const embed = new EmbedBuilder()
        .setTitle('🎛️ Reaction Role Panel Setup Dashboard')
        .setDescription(`Setting up reaction roles for ${config.channel}`)
        .setColor(0x5865F2)
        .addFields([
            {
                name: '📝 Title',
                value: config.title ? `✅ ${config.title}` : '❌ Not set',
                inline: true
            },
            {
                name: '📄 Description',
                value: config.description ? `✅ ${config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description}` : '❌ Not set',
                inline: true
            },
            {
                name: '⚙️ Mode',
                value: config.mode ? `✅ ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}` : '❌ Not set',
                inline: true
            },
            {
                name: '🎭 Roles',
                value: config.roles.length > 0 ? `✅ ${config.roles.length} role(s) configured` : '❌ No roles added',
                inline: false
            }
        ])
        .setFooter({ text: 'Configure all fields to enable panel creation' });

    // Create action buttons
    const row1 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_title_${configId}`)
                .setLabel('Set Title')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📝'),
            new ButtonBuilder()
                .setCustomId(`dashboard_description_${configId}`)
                .setLabel('Set Description')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📄'),
            new ButtonBuilder()
                .setCustomId(`dashboard_mode_${configId}`)
                .setLabel('Set Mode')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⚙️')
        );

    const row2 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_roles_${configId}`)
                .setLabel('Configure Roles')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🎭'),
            new ButtonBuilder()
                .setCustomId(`dashboard_preview_${configId}`)
                .setLabel('Preview Panel')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('👁️')
                .setDisabled(!isConfigComplete(config))
        );

    const row3 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_create_${configId}`)
                .setLabel('Create Panel')
                .setStyle(ButtonStyle.Success)
                .setEmoji('✅')
                .setDisabled(!isConfigComplete(config)),
            new ButtonBuilder()
                .setCustomId(`dashboard_cancel_${configId}`)
                .setLabel('Cancel')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('❌')
        );

    return {
        embeds: [embed],
        components: [row1, row2, row3]
    };
}

// Helper function to check if configuration is complete
function isConfigComplete(config) {
    return config.title && config.description && config.mode && config.roles.length > 0;
}

// Helper function to show title modal
async function showTitleModal(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_title_${configId}`)
        .setTitle('Set Panel Title');

    const titleInput = new TextInputBuilder()
        .setCustomId('title_input')
        .setLabel('Panel Title')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter the title for your reaction role panel')
        .setRequired(true)
        .setMaxLength(256);

    const actionRow = new ActionRowBuilder().addComponents(titleInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
}

// Helper function to show description modal
async function showDescriptionModal(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_description_${configId}`)
        .setTitle('Set Panel Description');

    const descriptionInput = new TextInputBuilder()
        .setCustomId('description_input')
        .setLabel('Panel Description')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('Enter the description for your reaction role panel')
        .setRequired(true)
        .setMaxLength(4000);

    const actionRow = new ActionRowBuilder().addComponents(descriptionInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
}

// Helper function to show mode selection
async function showModeSelect(interaction, configId) {
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`select_mode_${configId}`)
        .setPlaceholder('Choose role assignment mode')
        .addOptions([
            {
                label: 'Single Role',
                description: 'Users can only have one role from this panel',
                value: 'single',
                emoji: '1️⃣'
            },
            {
                label: 'Multiple Roles',
                description: 'Users can have multiple roles from this panel',
                value: 'multi',
                emoji: '🔢'
            }
        ]);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        content: '⚙️ **Select Role Assignment Mode:**',
        components: [row],
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to show role configuration (simplified for now)
async function showRoleSelect(interaction, configId) {
    await interaction.reply({
        content: '🎭 **Role Configuration**\n\n' +
                'Role configuration will be implemented in the next update. For now, please use the parameter-based command or contact an administrator.\n\n' +
                '**Available roles in this server:**\n' +
                interaction.guild.roles.cache
                    .filter(role => !role.managed && role.name !== '@everyone')
                    .map(role => `• ${role.name}`)
                    .slice(0, 10)
                    .join('\n') +
                (interaction.guild.roles.cache.size > 10 ? '\n• ... and more' : ''),
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to show preview
async function showPreview(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config || !isConfigComplete(config)) {
        return interaction.reply({
            content: '❌ Configuration is incomplete. Please fill all required fields first.',
            flags: MessageFlags.Ephemeral
        });
    }

    const previewEmbed = new EmbedBuilder()
        .setTitle(config.title)
        .setDescription(config.description)
        .setColor(0x5865F2)
        .setFooter({ text: 'This is a preview - no roles will be assigned from this message' });

    // Create preview buttons (disabled)
    const previewButtons = [];
    for (let i = 0; i < Math.min(config.roles.length, 5); i++) {
        const role = config.roles[i];
        previewButtons.push(
            new ButtonBuilder()
                .setCustomId(`preview_${i}`)
                .setLabel(role.label)
                .setEmoji(role.emoji)
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(true)
        );
    }

    const rows = [];
    for (let i = 0; i < previewButtons.length; i += 5) {
        const row = new ActionRowBuilder()
            .addComponents(previewButtons.slice(i, i + 5));
        rows.push(row);
    }

    await interaction.reply({
        content: '👁️ **Panel Preview:**',
        embeds: [previewEmbed],
        components: rows,
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to create the panel
async function createPanel(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config || !isConfigComplete(config)) {
        return interaction.reply({
            content: '❌ Configuration is incomplete. Please fill all required fields first.',
            flags: MessageFlags.Ephemeral
        });
    }

    try {
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles
        };

        const result = await reactionRoleHandler.createReactionRolePanel(
            config.channel,
            panelData,
            interaction.guild
        );

        if (result.success) {
            // Clean up the configuration
            dashboardConfigs.delete(configId);

            await interaction.reply({
                content: `✅ **Reaction role panel created successfully!**\n\n` +
                        `📍 **Channel:** ${config.channel}\n` +
                        `📝 **Title:** ${config.title}\n` +
                        `⚙️ **Mode:** ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}\n` +
                        `🎭 **Roles:** ${config.roles.length} role(s)\n` +
                        `🆔 **Panel ID:** \`${result.panelId}\``,
                flags: MessageFlags.Ephemeral
            });
        } else {
            await interaction.reply({
                content: `❌ Failed to create reaction role panel: ${result.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

    } catch (error) {
        console.error('Error creating panel:', error);
        await interaction.reply({
            content: `❌ An error occurred while creating the panel: ${error.message}`,
            flags: MessageFlags.Ephemeral
        });
    }
}

// Helper function to cancel dashboard
async function cancelDashboard(interaction, configId) {
    dashboardConfigs.delete(configId);

    await interaction.reply({
        content: '❌ **Setup cancelled.** The dashboard has been closed.',
        flags: MessageFlags.Ephemeral
    });
}
