const {
    <PERSON>lash<PERSON>ommandB<PERSON>er,
    PermissionFlagsBits,
    ChannelType,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    MessageFlags
} = require('discord.js');
const reactionRoleStorage = require('../utils/reactionRoleStorage');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive setup wizard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('channel');

            // Check if user already has an active setup session
            const existingSession = reactionRoleStorage.getSetupSessionByUserId(interaction.user.id);
            if (existingSession) {
                return interaction.reply({
                    content: '⚠️ You already have an active setup session. Please complete or cancel it first.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check channel permissions
            const channelPermissions = channel.permissionsFor(botMember);
            if (!channelPermissions.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.EmbedLinks])) {
                return interaction.reply({
                    content: `❌ I don't have permission to send messages and embeds in ${channel}.`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Create setup session
            const sessionId = reactionRoleStorage.createSetupSession(interaction.user.id, {
                guildId: interaction.guild.id,
                channelId: channel.id,
                userId: interaction.user.id
            });

            // Create setup wizard embed
            const embed = this.createSetupWizardEmbed({});
            const components = this.createSetupWizardComponents(sessionId);

            await interaction.reply({
                content: `🎛️ **Reaction Role Setup Wizard**\n\nSetting up reaction roles for ${channel}`,
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);

            // Clean up session on error
            try {
                const existingSession = reactionRoleStorage.getSetupSessionByUserId(interaction.user.id);
                if (existingSession) {
                    reactionRoleStorage.deleteSetupSession(existingSession.sessionId);
                }
            } catch (cleanupError) {
                console.error('Error cleaning up setup session:', cleanupError);
            }

            const errorMessage = `❌ An error occurred while starting the setup wizard: ${error.message}`;

            if (interaction.replied || interaction.deferred) {
                return interaction.editReply({ content: errorMessage, embeds: [], components: [] });
            } else {
                return interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    createSetupWizardEmbed(config) {
        const embed = new EmbedBuilder()
            .setTitle('🎛️ Reaction Role Panel Setup')
            .setDescription('Configure your reaction role panel step by step.')
            .setColor(0x5865F2);

        // Add fields showing current configuration
        embed.addFields([
            {
                name: '📝 Title',
                value: config.title ? `✅ ${config.title}` : '⚪ Not set',
                inline: true
            },
            {
                name: '📄 Description',
                value: config.description ? '✅ Set' : '⚪ Not set',
                inline: true
            },
            {
                name: '⚙️ Mode',
                value: config.mode ? `✅ ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}` : '⚪ Not set',
                inline: true
            },
            {
                name: '🎭 Roles',
                value: config.roles && config.roles.length > 0 ? `✅ ${config.roles.length} role(s)` : '⚪ Not set',
                inline: true
            },
            {
                name: '🖼️ Image',
                value: config.image_url ? '✅ Set' : '⚪ Optional',
                inline: true
            },
            {
                name: '📊 Status',
                value: this.getSetupStatus(config),
                inline: true
            }
        ]);

        return embed;
    },

    createSetupWizardComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_title_${sessionId}`)
                    .setLabel('Set Title')
                    .setEmoji('📝')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_description_${sessionId}`)
                    .setLabel('Set Description')
                    .setEmoji('📄')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_mode_${sessionId}`)
                    .setLabel('Set Mode')
                    .setEmoji('⚙️')
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_roles_${sessionId}`)
                    .setLabel('Configure Roles')
                    .setEmoji('🎭')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_image_${sessionId}`)
                    .setLabel('Set Image (Optional)')
                    .setEmoji('🖼️')
                    .setStyle(ButtonStyle.Secondary)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_preview_${sessionId}`)
                    .setLabel('Preview Panel')
                    .setEmoji('👁️')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`setup_create_${sessionId}`)
                    .setLabel('Create Panel')
                    .setEmoji('✅')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`setup_cancel_${sessionId}`)
                    .setLabel('Cancel')
                    .setEmoji('❌')
                    .setStyle(ButtonStyle.Danger)
            );

        return [row1, row2, row3];
    },

    getSetupStatus(config) {
        const required = ['title', 'description', 'mode', 'roles'];
        const completed = required.filter(field => {
            if (field === 'roles') {
                return config.roles && config.roles.length > 0;
            }
            return config[field];
        });

        if (completed.length === required.length) {
            return '✅ Ready to create';
        } else {
            return `⚪ ${completed.length}/${required.length} required fields`;
        }
    }
};