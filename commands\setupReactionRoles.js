const {
    <PERSON>lash<PERSON>ommandB<PERSON>er,
    PermissionFlagsBits,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ChannelType,
    StringSelectMenuBuilder
} = require('discord.js');
const reactionRoleStorage = require('../utils/reactionRoleStorage');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive setup wizard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        try {
            // Check if user already has an active setup session
            const existingSessions = reactionRoleStorage.getUserSetupSessions(interaction.user.id);
            if (existingSessions.length > 0) {
                // Clean up old sessions and continue
                for (const sessionId of existingSessions) {
                    reactionRoleStorage.deleteSetupSession(sessionId);
                }
            }

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const channel = interaction.options.getChannel('channel');

            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Create new setup session
            const sessionId = reactionRoleStorage.createSetupSession(
                interaction.user.id,
                interaction.guild.id,
                channel.id
            );

            // Create and send the setup wizard interface
            const wizardContent = this.createSetupWizardEmbed(sessionId, channel);

            await interaction.reply({
                ...wizardContent,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles command:', error);

            const errorMessage = `❌ An error occurred while starting the setup wizard: ${error.message}`;

            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    /**
     * Create the setup wizard embed and components
     * @param {string} sessionId - Setup session ID
     * @param {TextChannel} channel - Target channel
     * @returns {Object} Message content with embed and components
     */
    createSetupWizardEmbed(sessionId, channel) {
        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (!session) {
            throw new Error('Setup session not found');
        }

        // Create status indicators
        const titleStatus = session.title ? '✅' : '❌';
        const descriptionStatus = session.description ? '✅' : '❌';
        const modeStatus = session.mode ? '✅' : '❌';
        const rolesStatus = session.roles.length > 0 ? '✅' : '❌';
        const imageStatus = session.imageUrl ? '✅' : '⚪';

        // Check if all required fields are complete
        const isComplete = session.title && session.description && session.mode && session.roles.length > 0;

        const embed = new EmbedBuilder()
            .setTitle('🎛️ Reaction Role Setup Wizard')
            .setDescription(`Setting up a reaction role panel for ${channel}\n\n**Configuration Status:**`)
            .addFields([
                {
                    name: '📝 Required Configuration',
                    value: [
                        `${titleStatus} **Title** ${session.title ? `- "${session.title}"` : '- Not set'}`,
                        `${descriptionStatus} **Description** ${session.description ? '- Set' : '- Not set'}`,
                        `${modeStatus} **Mode** ${session.mode ? `- ${session.mode === 'multi' ? 'Multiple roles' : 'Single role'}` : '- Not set'}`,
                        `${rolesStatus} **Roles** ${session.roles.length > 0 ? `- ${session.roles.length} role(s) configured` : '- No roles added'}`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '🖼️ Optional Configuration',
                    value: `${imageStatus} **Image URL** ${session.imageUrl ? '- Set' : '- Not set'}`,
                    inline: false
                }
            ])
            .setColor(isComplete ? 0x00FF00 : 0xFFAA00)
            .setFooter({ text: `Session ID: ${sessionId}` });

        // Create action buttons
        const actionRow1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_title_${sessionId}`)
                    .setLabel('Set Title')
                    .setEmoji('📝')
                    .setStyle(session.title ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_description_${sessionId}`)
                    .setLabel('Set Description')
                    .setEmoji('📄')
                    .setStyle(session.description ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_mode_${sessionId}`)
                    .setLabel('Set Mode')
                    .setEmoji('⚙️')
                    .setStyle(session.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
            );

        const actionRow2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_roles_${sessionId}`)
                    .setLabel('Add Roles')
                    .setEmoji('👥')
                    .setStyle(session.roles.length > 0 ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_image_${sessionId}`)
                    .setLabel('Set Image')
                    .setEmoji('🖼️')
                    .setStyle(session.imageUrl ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`setup_preview_${sessionId}`)
                    .setLabel('Preview')
                    .setEmoji('👁️')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(!isComplete)
            );

        const actionRow3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_create_${sessionId}`)
                    .setLabel('Create Panel')
                    .setEmoji('✅')
                    .setStyle(ButtonStyle.Success)
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`setup_cancel_${sessionId}`)
                    .setLabel('Cancel')
                    .setEmoji('❌')
                    .setStyle(ButtonStyle.Danger)
            );

        return {
            embeds: [embed],
            components: [actionRow1, actionRow2, actionRow3]
        };
    }
