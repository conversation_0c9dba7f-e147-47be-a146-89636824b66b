const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle
} = require('discord.js');
const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles-dashboard')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        try {
            const channel = interaction.options.getChannel('channel');

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember);
            if (!permissionCheck.success) {
                return interaction.reply({
                    content: `❌ ${permissionCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check channel permissions
            const channelPermissions = channel.permissionsFor(botMember);
            if (!channelPermissions.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.EmbedLinks])) {
                return interaction.reply({
                    content: `❌ I don't have permission to send messages and embeds in ${channel}.`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Initialize dashboard configuration
            const configId = `${interaction.user.id}_${Date.now()}`;
            dashboardConfigs.set(configId, {
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                channel: channel,
                title: null,
                description: null,
                mode: null,
                roles: []
            });

            // Create and send dashboard
            const dashboardContent = createDashboard(configId);
            await interaction.reply({
                ...dashboardContent,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Error in setup-reaction-roles-dashboard command:', error);
            
            const errorMessage = `❌ An error occurred while creating the dashboard: ${error.message}`;

            if (interaction.replied || interaction.deferred) {
                return interaction.editReply({ content: errorMessage });
            } else {
                return interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Handle button interactions for the dashboard
    async handleDashboardButton(interaction) {
        try {
            const customId = interaction.customId;
            const parts = customId.split('_');
            const action = parts[1];
            const configId = parts.slice(2).join('_'); // Rejoin in case configId has underscores

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            if (config.userId !== interaction.user.id) {
                return interaction.reply({
                    content: '❌ You can only interact with your own dashboard.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'title':
                    await showTitleModal(interaction, configId);
                    break;
                case 'description':
                    await showDescriptionModal(interaction, configId);
                    break;
                case 'mode':
                    await showModeSelect(interaction, configId);
                    break;
                case 'roles':
                    await showRoleSelect(interaction, configId);
                    break;
                case 'preview':
                    await showPreview(interaction, configId);
                    break;
                case 'create':
                    await createPanel(interaction, configId);
                    break;
                case 'cancel':
                    await cancelDashboard(interaction, configId);
                    break;
                case 'save':
                    await saveConfiguration(interaction, configId);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

            // Handle role management buttons
            if (customId.startsWith('role_')) {
                const roleAction = parts[1];
                const roleConfigId = parts.slice(2).join('_');

                const roleConfig = dashboardConfigs.get(roleConfigId);
                if (!roleConfig) {
                    return interaction.reply({
                        content: '❌ Dashboard session expired. Please run the command again.',
                        flags: MessageFlags.Ephemeral
                    });
                }

                switch (roleAction) {
                    case 'add':
                        await showAddRoleModal(interaction, roleConfigId);
                        break;
                    case 'remove':
                        await removeLastRole(interaction, roleConfigId);
                        break;
                    case 'done':
                        const updatedDashboard = createDashboard(roleConfigId);
                        await interaction.update(updatedDashboard);
                        break;
                    default:
                        await interaction.reply({
                            content: '❌ Unknown role action.',
                            flags: MessageFlags.Ephemeral
                        });
                }
                return;
            }

        } catch (error) {
            console.error('Error handling dashboard button:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        try {
            const customId = interaction.customId;
            const parts = customId.split('_');
            const action = parts[1];
            const configId = parts.slice(2).join('_'); // Rejoin in case configId has underscores

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'title':
                    const title = interaction.fields.getTextInputValue('title_input');
                    config.title = title;
                    dashboardConfigs.set(configId, config);

                    const titleDashboard = createDashboard(configId);
                    await interaction.update(titleDashboard);
                    break;

                case 'description':
                    const description = interaction.fields.getTextInputValue('description_input');
                    config.description = description;
                    dashboardConfigs.set(configId, config);

                    const descDashboard = createDashboard(configId);
                    await interaction.update(descDashboard);
                    break;

                case 'role':
                    await handleAddRoleModal(interaction, configId, config);
                    break;

                case 'save':
                    await handleSaveConfigModal(interaction, configId, config);
                    break;

                default:
                    await interaction.reply({
                        content: '❌ Unknown modal action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('Error handling dashboard modal:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        try {
            const customId = interaction.customId;
            const parts = customId.split('_');
            const action = parts[1];
            const configId = parts.slice(2).join('_'); // Rejoin in case configId has underscores

            const config = dashboardConfigs.get(configId);
            if (!config) {
                return interaction.reply({
                    content: '❌ Dashboard session expired. Please run the command again.',
                    flags: MessageFlags.Ephemeral
                });
            }

            switch (action) {
                case 'mode':
                    const mode = interaction.values[0];
                    config.mode = mode;
                    dashboardConfigs.set(configId, config);
                    
                    const modeDashboard = createDashboard(configId);
                    await interaction.update(modeDashboard);
                    break;

                default:
                    await interaction.reply({
                        content: '❌ Unknown select action.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('Error handling dashboard select:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }
};

// Helper function to create the dashboard embed and buttons
function createDashboard(configId) {
    const config = dashboardConfigs.get(configId);
    if (!config) return null;

    const embed = new EmbedBuilder()
        .setTitle('🎛️ Reaction Role Panel Setup Dashboard')
        .setDescription(`Setting up reaction roles for ${config.channel}`)
        .setColor(0x5865F2)
        .addFields([
            {
                name: '📝 Title',
                value: config.title ? `✅ ${config.title}` : '❌ Not set',
                inline: true
            },
            {
                name: '📄 Description', 
                value: config.description ? `✅ ${config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description}` : '❌ Not set',
                inline: true
            },
            {
                name: '⚙️ Mode',
                value: config.mode ? `✅ ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}` : '❌ Not set',
                inline: true
            },
            {
                name: '🎭 Roles',
                value: config.roles.length > 0 ? `✅ ${config.roles.length} role(s) configured` : '❌ No roles added',
                inline: false
            }
        ])
        .setFooter({ text: 'Configure all fields to enable panel creation' });

    // Create action buttons
    const row1 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_title_${configId}`)
                .setLabel('Set Title')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📝'),
            new ButtonBuilder()
                .setCustomId(`dashboard_description_${configId}`)
                .setLabel('Set Description')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📄'),
            new ButtonBuilder()
                .setCustomId(`dashboard_mode_${configId}`)
                .setLabel('Set Mode')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⚙️')
        );

    const row2 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_roles_${configId}`)
                .setLabel('Configure Roles')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🎭'),
            new ButtonBuilder()
                .setCustomId(`dashboard_preview_${configId}`)
                .setLabel('Preview Panel')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('👁️')
                .setDisabled(!isConfigComplete(config))
        );

    const row3 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`dashboard_create_${configId}`)
                .setLabel('Create Panel')
                .setStyle(ButtonStyle.Success)
                .setEmoji('✅')
                .setDisabled(!isConfigComplete(config)),
            new ButtonBuilder()
                .setCustomId(`dashboard_save_${configId}`)
                .setLabel('Save Config')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('💾')
                .setDisabled(!isConfigComplete(config)),
            new ButtonBuilder()
                .setCustomId(`dashboard_cancel_${configId}`)
                .setLabel('Cancel')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('❌')
        );

    return {
        embeds: [embed],
        components: [row1, row2, row3]
    };
}

// Helper function to check if configuration is complete
function isConfigComplete(config) {
    return config.title && config.description && config.mode && config.roles.length > 0;
}

// Helper function to show title modal
async function showTitleModal(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_title_${configId}`)
        .setTitle('Set Panel Title');

    const titleInput = new TextInputBuilder()
        .setCustomId('title_input')
        .setLabel('Panel Title')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter the title for your reaction role panel')
        .setRequired(true)
        .setMaxLength(256);

    const actionRow = new ActionRowBuilder().addComponents(titleInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
}

// Helper function to show description modal
async function showDescriptionModal(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_description_${configId}`)
        .setTitle('Set Panel Description');

    const descriptionInput = new TextInputBuilder()
        .setCustomId('description_input')
        .setLabel('Panel Description')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('Enter the description for your reaction role panel')
        .setRequired(true)
        .setMaxLength(4000);

    const actionRow = new ActionRowBuilder().addComponents(descriptionInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
}

// Helper function to show mode selection
async function showModeSelect(interaction, configId) {
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`select_mode_${configId}`)
        .setPlaceholder('Choose role assignment mode')
        .addOptions([
            {
                label: 'Single Role',
                description: 'Users can only have one role from this panel',
                value: 'single',
                emoji: '1️⃣'
            },
            {
                label: 'Multiple Roles',
                description: 'Users can have multiple roles from this panel',
                value: 'multi',
                emoji: '🔢'
            }
        ]);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        content: '⚙️ **Select Role Assignment Mode:**',
        components: [row],
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to show role configuration interface
async function showRoleSelect(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config) {
        return interaction.reply({
            content: '❌ Dashboard session expired. Please run the command again.',
            flags: MessageFlags.Ephemeral
        });
    }

    // Create role management interface
    const embed = new EmbedBuilder()
        .setTitle('🎭 Role Configuration')
        .setDescription('Manage roles for your reaction role panel')
        .setColor(0x5865F2)
        .addFields([
            {
                name: '📋 Current Roles',
                value: config.roles.length > 0
                    ? config.roles.map((role, index) =>
                        `${index + 1}. ${role.emoji} **${role.label}** (<@&${role.role_id}>)`
                    ).join('\n')
                    : 'No roles configured yet',
                inline: false
            },
            {
                name: '📝 Instructions',
                value: '• Click **Add Role** to add a new role\n' +
                       '• Click **Remove Role** to remove the last role\n' +
                       '• You can add up to 25 roles per panel',
                inline: false
            }
        ]);

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`role_add_${configId}`)
                .setLabel('Add Role')
                .setStyle(ButtonStyle.Success)
                .setEmoji('➕')
                .setDisabled(config.roles.length >= 25),
            new ButtonBuilder()
                .setCustomId(`role_remove_${configId}`)
                .setLabel('Remove Last Role')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('➖')
                .setDisabled(config.roles.length === 0),
            new ButtonBuilder()
                .setCustomId(`role_done_${configId}`)
                .setLabel('Done')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('✅')
        );

    await interaction.reply({
        embeds: [embed],
        components: [row],
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to show preview
async function showPreview(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config || !isConfigComplete(config)) {
        return interaction.reply({
            content: '❌ Configuration is incomplete. Please fill all required fields first.',
            flags: MessageFlags.Ephemeral
        });
    }

    const previewEmbed = new EmbedBuilder()
        .setTitle(config.title)
        .setDescription(config.description)
        .setColor(0x5865F2)
        .setFooter({ text: 'This is a preview - no roles will be assigned from this message' });

    // Create preview buttons for ALL roles (up to 25 roles, 5 per row)
    const components = [];
    const roles = config.roles;

    for (let i = 0; i < roles.length; i += 5) {
        const row = new ActionRowBuilder();
        const rowRoles = roles.slice(i, i + 5);

        for (const role of rowRoles) {
            try {
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`preview_role_${role.role_id}`)
                        .setLabel(role.label)
                        .setEmoji(role.emoji)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true) // Disabled for preview
                );
            } catch (error) {
                console.error(`Error creating preview button for role ${role.role_id}:`, error);
                // Create button without emoji if emoji is invalid
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`preview_role_${role.role_id}`)
                        .setLabel(role.label)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true)
                );
            }
        }

        components.push(row);
    }

    await interaction.reply({
        content: '👁️ **Panel Preview:**\n*This is exactly how your reaction role panel will look when created (buttons are disabled in preview)*',
        embeds: [previewEmbed],
        components: components,
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to create the panel
async function createPanel(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config || !isConfigComplete(config)) {
        return interaction.reply({
            content: '❌ Configuration is incomplete. Please fill all required fields first.',
            flags: MessageFlags.Ephemeral
        });
    }

    try {
        // Validate all roles exist and are accessible
        const invalidRoles = [];
        for (const roleConfig of config.roles) {
            const role = interaction.guild.roles.cache.get(roleConfig.role_id);
            if (!role) {
                invalidRoles.push(`Role ID ${roleConfig.role_id} (${roleConfig.label})`);
            } else if (role.managed) {
                invalidRoles.push(`${role.name} (managed by integration)`);
            }
        }

        if (invalidRoles.length > 0) {
            return interaction.reply({
                content: `❌ **Cannot create panel - invalid roles detected:**\n\n` +
                        invalidRoles.map(role => `• ${role}`).join('\n') + '\n\n' +
                        'Please remove these roles and try again.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Check bot permissions in target channel
        const channelPermissions = config.channel.permissionsFor(interaction.guild.members.me);
        if (!channelPermissions.has([PermissionFlagsBits.SendMessages, PermissionFlagsBits.EmbedLinks])) {
            return interaction.reply({
                content: `❌ **Missing permissions!**\n\nI don't have permission to send messages and embeds in ${config.channel}.\n\n` +
                        'Please ensure I have the following permissions in that channel:\n' +
                        '• Send Messages\n' +
                        '• Embed Links',
                flags: MessageFlags.Ephemeral
            });
        }

        // Create panel data in the expected format
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles // Already in correct format: [{role_id, emoji, label}, ...]
        };

        console.log('Creating panel with data:', JSON.stringify(panelData, null, 2));

        const result = await reactionRoleHandler.createReactionRolePanel(
            config.channel,
            panelData,
            interaction.guild
        );

        if (result.success) {
            // Clean up the configuration
            dashboardConfigs.delete(configId);

            await interaction.reply({
                content: `✅ **Reaction role panel created successfully!**\n\n` +
                        `📍 **Channel:** ${config.channel}\n` +
                        `📝 **Title:** ${config.title}\n` +
                        `📄 **Description:** ${config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description}\n` +
                        `⚙️ **Mode:** ${config.mode === 'single' ? 'Single Role (users can only have one role from this panel)' : 'Multiple Roles (users can have multiple roles from this panel)'}\n` +
                        `🎭 **Roles:** ${config.roles.length} role(s) configured\n` +
                        `🆔 **Panel ID:** \`${result.panelId}\`\n` +
                        `💬 **Message ID:** \`${result.messageId}\`\n\n` +
                        `The panel is now live and users can interact with it!`,
                flags: MessageFlags.Ephemeral
            });
        } else {
            await interaction.reply({
                content: `❌ **Failed to create reaction role panel:**\n\n${result.error}\n\n` +
                        'Please check the configuration and try again.',
                flags: MessageFlags.Ephemeral
            });
        }

    } catch (error) {
        console.error('Error creating panel:', error);
        await interaction.reply({
            content: `❌ **An error occurred while creating the panel:**\n\n\`${error.message}\`\n\n` +
                    'Please try again or contact an administrator if the problem persists.',
            flags: MessageFlags.Ephemeral
        });
    }
}

// Helper function to cancel dashboard
async function cancelDashboard(interaction, configId) {
    dashboardConfigs.delete(configId);

    await interaction.reply({
        content: '❌ **Setup cancelled.** The dashboard has been closed.',
        flags: MessageFlags.Ephemeral
    });
}

// Helper function to show add role modal
async function showAddRoleModal(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_role_${configId}`)
        .setTitle('Add Role to Panel');

    const roleInput = new TextInputBuilder()
        .setCustomId('role_input')
        .setLabel('Role Name or ID')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter role name (e.g., "Member") or role ID')
        .setRequired(true)
        .setMaxLength(100);

    const emojiInput = new TextInputBuilder()
        .setCustomId('emoji_input')
        .setLabel('Emoji')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter emoji (e.g., 🎭, :custom_emoji:, or Unicode)')
        .setRequired(true)
        .setMaxLength(50);

    const labelInput = new TextInputBuilder()
        .setCustomId('label_input')
        .setLabel('Button Label')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter the text to display on the button')
        .setRequired(true)
        .setMaxLength(80);

    const row1 = new ActionRowBuilder().addComponents(roleInput);
    const row2 = new ActionRowBuilder().addComponents(emojiInput);
    const row3 = new ActionRowBuilder().addComponents(labelInput);

    modal.addComponents(row1, row2, row3);
    await interaction.showModal(modal);
}

// Helper function to handle add role modal submission
async function handleAddRoleModal(interaction, configId, config) {
    try {
        const roleInput = interaction.fields.getTextInputValue('role_input');
        const emojiInput = interaction.fields.getTextInputValue('emoji_input');
        const labelInput = interaction.fields.getTextInputValue('label_input');

        // Find the role by name or ID
        let role = null;

        // Try to find by ID first
        if (/^\d+$/.test(roleInput)) {
            role = interaction.guild.roles.cache.get(roleInput);
        }

        // If not found by ID, try to find by name
        if (!role) {
            role = interaction.guild.roles.cache.find(r =>
                r.name.toLowerCase() === roleInput.toLowerCase()
            );
        }

        if (!role) {
            return interaction.reply({
                content: `❌ **Role not found!**\n\nCould not find a role with name or ID: \`${roleInput}\`\n\n` +
                        '**Tips:**\n' +
                        '• Make sure the role name is spelled correctly\n' +
                        '• Try using the role ID instead (right-click role → Copy ID)\n' +
                        '• Ensure the role exists in this server',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate role
        if (role.managed) {
            return interaction.reply({
                content: `❌ **Cannot use managed role!**\n\nThe role "${role.name}" is managed by an integration (bot/app) and cannot be used in reaction roles.`,
                flags: MessageFlags.Ephemeral
            });
        }

        if (role.name === '@everyone') {
            return interaction.reply({
                content: '❌ **Cannot use @everyone role!**\n\nThe @everyone role cannot be used in reaction roles.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Check if role is already added
        if (config.roles.some(r => r.role_id === role.id)) {
            return interaction.reply({
                content: `❌ **Role already added!**\n\nThe role "${role.name}" is already configured in this panel.`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate emoji (basic validation)
        let processedEmoji = emojiInput.trim();

        // Check if it's a custom emoji format
        if (processedEmoji.startsWith('<:') && processedEmoji.endsWith('>')) {
            // Custom emoji format is valid
        } else if (processedEmoji.startsWith(':') && processedEmoji.endsWith(':')) {
            // Discord emoji shortcode format
        } else {
            // Assume it's a Unicode emoji - basic validation
            if (processedEmoji.length > 10) {
                return interaction.reply({
                    content: '❌ **Invalid emoji!**\n\nPlease use a valid emoji format:\n' +
                            '• Unicode emoji: 🎭\n' +
                            '• Discord shortcode: :tada:\n' +
                            '• Custom emoji: <:name:id>',
                    flags: MessageFlags.Ephemeral
                });
            }
        }

        // Add the role to configuration
        const newRole = {
            role_id: role.id,
            emoji: processedEmoji,
            label: labelInput.trim()
        };

        config.roles.push(newRole);
        dashboardConfigs.set(configId, config);

        // Update the role configuration interface
        await showRoleSelect(interaction, configId);

    } catch (error) {
        console.error('Error handling add role modal:', error);
        await interaction.reply({
            content: `❌ An error occurred while adding the role: ${error.message}`,
            flags: MessageFlags.Ephemeral
        });
    }
}

// Helper function to remove the last role
async function removeLastRole(interaction, configId) {
    const config = dashboardConfigs.get(configId);
    if (!config || config.roles.length === 0) {
        return interaction.reply({
            content: '❌ No roles to remove.',
            flags: MessageFlags.Ephemeral
        });
    }

    const removedRole = config.roles.pop();
    dashboardConfigs.set(configId, config);

    await interaction.reply({
        content: `✅ **Role removed!**\n\nRemoved: ${removedRole.emoji} **${removedRole.label}**`,
        flags: MessageFlags.Ephemeral
    });

    // Update the role configuration interface after a short delay
    setTimeout(async () => {
        try {
            await showRoleSelect(interaction, configId);
        } catch (error) {
            console.error('Error updating role interface after removal:', error);
        }
    }, 1000);
}

// Helper function to save configuration
async function saveConfiguration(interaction, configId) {
    const modal = new ModalBuilder()
        .setCustomId(`modal_save_${configId}`)
        .setTitle('Save Configuration');

    const nameInput = new TextInputBuilder()
        .setCustomId('config_name_input')
        .setLabel('Configuration Name')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter a name for this configuration')
        .setRequired(true)
        .setMaxLength(50);

    const row = new ActionRowBuilder().addComponents(nameInput);
    modal.addComponents(row);

    await interaction.showModal(modal);
}

// Helper function to handle save config modal
async function handleSaveConfigModal(interaction, configId, config) {
    try {
        const configName = interaction.fields.getTextInputValue('config_name_input');

        // Create saved configuration data
        const savedConfig = {
            name: configName,
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles,
            savedAt: new Date().toISOString(),
            savedBy: interaction.user.id,
            guildId: interaction.guild.id
        };

        // Save to persistent storage (using existing storage system)
        const fs = require('fs').promises;
        const path = require('path');

        const configsDir = path.join(__dirname, '..', 'data', 'saved_configs');
        const configFile = path.join(configsDir, `${interaction.guild.id}.json`);

        // Ensure directory exists
        await fs.mkdir(configsDir, { recursive: true });

        // Load existing configs or create new object
        let savedConfigs = {};
        try {
            const data = await fs.readFile(configFile, 'utf8');
            savedConfigs = JSON.parse(data);
        } catch (error) {
            // File doesn't exist or is invalid, start with empty object
            savedConfigs = {};
        }

        // Generate unique config ID
        const saveId = `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        savedConfigs[saveId] = savedConfig;

        // Save to file
        await fs.writeFile(configFile, JSON.stringify(savedConfigs, null, 2));

        await interaction.reply({
            content: `✅ **Configuration saved successfully!**\n\n` +
                    `📝 **Name:** ${configName}\n` +
                    `🆔 **Save ID:** \`${saveId}\`\n` +
                    `📅 **Saved:** ${new Date().toLocaleString()}\n\n` +
                    `You can load this configuration later using the save ID.`,
            flags: MessageFlags.Ephemeral
        });

    } catch (error) {
        console.error('Error saving configuration:', error);
        await interaction.reply({
            content: `❌ Failed to save configuration: ${error.message}`,
            flags: MessageFlags.Ephemeral
        });
    }
}
